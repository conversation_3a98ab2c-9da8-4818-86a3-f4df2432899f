import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { SUIConfig } from '@/lib/types';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus, Settings, Globe, Shield, ArrowUpRight } from 'lucide-react-native';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { ScrollView, StyleSheet, View, Alert, TouchableOpacity, SafeAreaView, RefreshControl } from 'react-native';

// 出站配置类型定义
interface OutboundConfig {
  id: string;
  tag: string;
  protocol: string;
  settings: any;
  streamSettings?: any;
  proxySettings?: any;
  mux?: any;
}

export default function SUIOutboundsScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const { configs } = useAppStore();

  // 直接从configs中计算config
  const config = useMemo(() => {
    if (!configId) return null;
    return configs.find(c => c.id === configId) as SUIConfig || null;
  }, [configId, configs]);

  const [outbounds, setOutbounds] = useState<OutboundConfig[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 加载出站配置
  const loadOutbounds = useCallback(async () => {
    if (!config) return;

    try {
      setIsLoading(true);
      // TODO: 实现S-UI出站配置获取API
      // const result = await getSUIOutbounds(config);
      // setOutbounds(result || []);
      
      // 临时模拟数据
      setOutbounds([]);
    } catch (error) {
      console.error('Failed to load S-UI outbounds:', error);
      Alert.alert(t('common.error'), t('sui.outbounds.loadFailed'));
    } finally {
      setIsLoading(false);
    }
  }, [config, t]);

  // 刷新数据
  const refreshOutbounds = useCallback(async () => {
    setIsRefreshing(true);
    await loadOutbounds();
    setIsRefreshing(false);
  }, [loadOutbounds]);

  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      loadOutbounds();
    }, [loadOutbounds])
  );

  // 添加出站
  const handleAddOutbound = () => {
    // TODO: 导航到出站配置页面
    Alert.alert(t('common.info'), 'Add outbound functionality will be implemented');
  };

  // 编辑出站
  const handleEditOutbound = (outbound: OutboundConfig) => {
    // TODO: 导航到出站编辑页面
    Alert.alert(t('common.info'), `Edit outbound: ${outbound.tag}`);
  };

  // 删除出站
  const handleDeleteOutbound = (outbound: OutboundConfig) => {
    Alert.alert(
      t('sui.outbounds.deleteOutbound'),
      t('sui.outbounds.deleteOutboundConfirm', { name: outbound.tag }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: 实现删除API
              Alert.alert(t('common.success'), t('sui.outbounds.deleteSuccess'));
              await loadOutbounds();
            } catch (error) {
              Alert.alert(t('common.error'), t('sui.outbounds.deleteFailed'));
            }
          },
        },
      ]
    );
  };

  // 获取协议图标
  const getProtocolIcon = (protocol: string) => {
    switch (protocol.toLowerCase()) {
      case 'freedom':
        return <Globe size={20} color="#10b981" />;
      case 'blackhole':
        return <Shield size={20} color="#ef4444" />;
      default:
        return <ArrowUpRight size={20} color="#3b82f6" />;
    }
  };

  // 获取协议颜色
  const getProtocolColor = (protocol: string): string => {
    switch (protocol.toLowerCase()) {
      case 'freedom':
        return '#10b981';
      case 'blackhole':
        return '#ef4444';
      case 'vmess':
      case 'vless':
        return '#3b82f6';
      case 'trojan':
        return '#8b5cf6';
      case 'shadowsocks':
        return '#f59e0b';
      default:
        return '#6b7280';
    }
  };

  // 格式化出站设置
  const formatOutboundSettings = (outbound: OutboundConfig): string => {
    const settings: string[] = [];
    
    if (outbound.protocol === 'freedom') {
      settings.push('Direct Connection');
    } else if (outbound.protocol === 'blackhole') {
      settings.push('Block Connection');
    } else if (outbound.settings) {
      // 根据不同协议显示相关信息
      if (outbound.settings.vnext && outbound.settings.vnext.length > 0) {
        const server = outbound.settings.vnext[0];
        settings.push(`${server.address}:${server.port}`);
      } else if (outbound.settings.servers && outbound.settings.servers.length > 0) {
        const server = outbound.settings.servers[0];
        settings.push(`${server.address}:${server.port}`);
      }
    }
    
    return settings.length > 0 ? settings.join(' | ') : t('sui.outbounds.noSettings');
  };

  if (!config) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: textColor + '60' }]}>
            {t('common.configNotFound')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={refreshOutbounds}
            tintColor={textColor}
          />
        }
      >
        {/* 头部操作栏 */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: textColor }]}>
            {t('sui.outbounds.title')}
          </Text>
          <Button
            onPress={handleAddOutbound}
            style={[styles.addButton, { borderColor }]}
            variant="outline"
          >
            <Plus size={16} color={textColor} />
            <Text style={[styles.addButtonText, { color: textColor }]}>
              {t('sui.outbounds.addOutbound')}
            </Text>
          </Button>
        </View>

        {/* 出站列表 */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: textColor + '60' }]}>
              {t('common.loading')}
            </Text>
          </View>
        ) : outbounds.length === 0 ? (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: textColor + '60' }]}>
              {t('sui.outbounds.noOutbounds')}
            </Text>
            <Text style={[styles.noDataSubtext, { color: textColor + '40' }]}>
              {t('sui.outbounds.noOutboundsDesc')}
            </Text>
          </View>
        ) : (
          <View style={styles.outboundsList}>
            {outbounds.map((outbound) => (
              <View key={outbound.id} style={[styles.outboundCard, { backgroundColor, borderColor }]}>
                {/* 卡片头部 */}
                <View style={styles.cardHeader}>
                  <View style={styles.cardTitleContainer}>
                    <View style={styles.protocolContainer}>
                      {getProtocolIcon(outbound.protocol)}
                      <Text style={[styles.cardTitle, { color: textColor }]}>
                        {outbound.tag}
                      </Text>
                    </View>
                    <Badge
                      style={[styles.protocolBadge, { backgroundColor: getProtocolColor(outbound.protocol) + '20' }]}
                    >
                      <Text style={[styles.badgeText, { color: getProtocolColor(outbound.protocol) }]}>
                        {outbound.protocol.toUpperCase()}
                      </Text>
                    </Badge>
                  </View>
                </View>

                {/* 卡片内容 */}
                <View style={styles.cardContent}>
                  <Text style={[styles.settingsText, { color: textColor + '80' }]}>
                    {formatOutboundSettings(outbound)}
                  </Text>
                  
                  {outbound.streamSettings && (
                    <View style={styles.streamInfo}>
                      <Text style={[styles.streamLabel, { color: textColor + '60' }]}>
                        {t('sui.outbounds.transport')}:
                      </Text>
                      <Text style={[styles.streamValue, { color: textColor }]}>
                        {outbound.streamSettings.network || 'tcp'}
                      </Text>
                    </View>
                  )}
                  
                  {outbound.mux && outbound.mux.enabled && (
                    <View style={styles.muxInfo}>
                      <Badge variant="secondary" style={styles.muxBadge}>
                        <Text style={styles.muxText}>
                          {t('sui.outbounds.muxEnabled')}
                        </Text>
                      </Badge>
                    </View>
                  )}
                </View>

                {/* 卡片操作 */}
                <View style={styles.cardActions}>
                  <TouchableOpacity
                    onPress={() => handleEditOutbound(outbound)}
                    style={[styles.actionButton, { borderColor }]}
                  >
                    <Settings size={16} color={textColor} />
                    <Text style={[styles.actionButtonText, { color: textColor }]}>
                      {t('common.edit')}
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    onPress={() => handleDeleteOutbound(outbound)}
                    style={[styles.actionButton, styles.deleteButton]}
                  >
                    <Text style={[styles.actionButtonText, { color: '#ef4444' }]}>
                      {t('common.delete')}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  addButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  noDataText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  outboundsList: {
    gap: 12,
  },
  outboundCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  cardHeader: {
    marginBottom: 12,
  },
  cardTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  protocolContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  protocolBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  cardContent: {
    gap: 8,
    marginBottom: 12,
  },
  settingsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  streamInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  streamLabel: {
    fontSize: 12,
  },
  streamValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  muxInfo: {
    marginTop: 4,
  },
  muxBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  muxText: {
    fontSize: 10,
  },
  cardActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    gap: 4,
  },
  deleteButton: {
    borderColor: '#ef4444',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
