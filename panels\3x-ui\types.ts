// 3X-UI 特有的类型定义

// 入站协议类型定义
export const Protocols = {
  VMESS: 'vmess',
  VLESS: 'vless',
  TROJAN: 'trojan',
  SHADOWSOCKS: 'shadowsocks',
  DOKODEMO: 'dokodemo-door',
  SOCKS: 'socks',
  HTTP: 'http',
  WIREGUARD: 'wireguard',
} as const;

export type InboundProtocol = typeof Protocols[keyof typeof Protocols];

// 传输方式类型定义
export const TransportTypes = {
  TCP: 'tcp',
  XHTTP: 'xhttp',
  MKCP: 'kcp',
  GRPC: 'grpc',
  WEBSOCKET: 'ws',
  HTTPUPGRADE: 'httpupgrade',
} as const;

export type TransportType = typeof TransportTypes[keyof typeof TransportTypes];

// 安全类型定义
export const SecurityTypes = {
  NONE: 'none',
  TLS: 'tls',
  REALITY: 'reality',
} as const;

export type SecurityType = typeof SecurityTypes[keyof typeof SecurityTypes];

// 用户配置类型定义
export interface VlessUser {
  id: string;
  email: string;
  flow?: string; // XTLS flow，仅在TCP+TLS/Reality时使用
}

export interface VmessUser {
  id: string;
  email: string;
}

export interface TrojanUser {
  password: string;
  email: string;
}

export interface ShadowsocksUser {
  password: string;
  method: string;
}

export interface HttpUser {
  user: string;
  pass: string;
}

export interface SocksUser {
  user: string;
  pass: string;
}

export interface WireguardPeer {
  privateKey?: string;
  publicKey: string;
  allowedIPs: string[];
  keepAlive?: number;
}

// Fallback配置类型
export interface Fallback {
  name?: string;
  alpn?: string;
  path?: string;
  dest: number;
  xver?: number;
}

// 协议设置类型定义
export interface VlessSettings {
  clients: VlessUser[];
  decryption: 'none';
  fallbacks?: Fallback[];
}

export interface VmessSettings {
  clients: VmessUser[];
}

export interface TrojanSettings {
  clients: TrojanUser[];
  fallbacks?: Fallback[];
}

export interface ShadowsocksSettings {
  network: string;
  method: string;
  password: string;
  clients?: ShadowsocksUser[];
}

export interface HttpSettings {
  accounts?: HttpUser[];
  allowTransparent?: boolean;
}

export interface SocksSettings {
  auth: 'noauth' | 'password';
  accounts?: SocksUser[];
  udp?: boolean;
  ip?: string;
  userLevel?: number;
}

export interface DokodemoSettings {
  address: string;
  port: number;
  network: string;
  followRedirect?: boolean;
}

export interface WireguardSettings {
  secretKey: string;
  peers: WireguardPeer[];
  mtu?: number;
}

// 传输配置类型定义
export interface TcpSettings {
  header?: {
    type: string;
    request?: any;
    response?: any;
  };
}

export interface XhttpSettings {
  path?: string;
  host?: string;
}

export interface MkcpSettings {
  mtu?: number;
  tti?: number;
  uplinkCapacity?: number;
  downlinkCapacity?: number;
  congestion?: boolean;
  readBufferSize?: number;
  writeBufferSize?: number;
  header?: {
    type: string;
  };
}

export interface GrpcSettings {
  serviceName?: string;
  multiMode?: boolean;
}

export interface WebSocketSettings {
  path?: string;
  headers?: Record<string, string>;
}

export interface HttpUpgradeSettings {
  path?: string;
  host?: string;
  headers?: Record<string, string>;
}

// 安全配置类型定义
export interface TlsSettings {
  serverName?: string;
  allowInsecure?: boolean;
  alpn?: string[];
  certificates?: Array<{
    certificateFile?: string;
    keyFile?: string;
    certificate?: string[];
    key?: string[];
  }>;
}

export interface RealitySettings {
  show?: boolean;
  dest?: string;
  xver?: number;
  serverNames?: string[];
  privateKey?: string;
  minClientVer?: string;
  maxClientVer?: string;
  maxTimeDiff?: number;
  shortIds?: string[];
}

// SockOpt配置类型定义
export interface SockOptSettings {
  mark?: number;
  tcpFastOpen?: boolean;
  tproxy?: string;
  domainStrategy?: string;
  dialerProxy?: string;
}

// 入站配置类型定义
export interface InboundConfig {
  id?: number; // API响应中的ID
  listen: string;
  port: number;
  protocol: InboundProtocol;
  settings: any;
  streamSettings?: any;
  tag?: string;
  sniffing?: {
    enabled: boolean;
    destOverride: string[];
    metadataOnly?: boolean;
    domainsExcluded?: string[];
    routeOnly?: boolean;
  };
  allocate?: {
    strategy: string;
    refresh: number;
    concurrency: number;
  };
  expiryTime?: number; // 有效期（天数）
  totalTraffic?: number; // 总流量（GB）
  // API响应字段
  up?: number; // 上行流量
  down?: number; // 下行流量
  total?: number; // 总流量限制
  remark?: string; // 备注
  enable?: boolean; // 是否启用
  clientStats?: any[]; // 客户端统计
}

// 出站协议类型定义
export enum OutboundProtocols {
  BLACKHOLE = 'blackhole',
  DNS = 'dns',
  FREEDOM = 'freedom',
  HTTP = 'http',
  SHADOWSOCKS = 'shadowsocks',
  SOCKS = 'socks',
  TROJAN = 'trojan',
  VLESS = 'vless',
  VMESS = 'vmess',
  WIREGUARD = 'wireguard'
}

// 出站配置类型定义
export interface OutboundConfig {
  sendThrough?: string | null;
  protocol: string;
  settings: any;
  tag: string;
  streamSettings?: any;
  proxySettings?: {
    tag: string;
  };
  mux?: {
    enabled: boolean;
    concurrency: number;
    xudpConcurrency?: number;
    xudpProxyUDP443?: string;
  };
}

// 3X-UI 服务器配置类型定义
export interface ThreeXUIServerConfig {
  inbounds: InboundConfig[];
  xray?: {
    outbounds: OutboundConfig[];
    [key: string]: any;
  };
}
