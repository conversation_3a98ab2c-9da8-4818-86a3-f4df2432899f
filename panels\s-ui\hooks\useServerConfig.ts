import { useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { SUIConfig } from '@/lib/types';
import { getSUIServerConfig } from '../utils';
import { useAppStore } from '@/lib/store';

/**
 * 使用 useFocusEffect 自动获取 S-UI 服务器配置的 Hook
 * @param config S-UI 配置对象
 * @param enabled 是否启用自动获取，默认为 true
 */
export function useServerConfig(config: SUIConfig | null, enabled: boolean = true) {
  const fetchServerConfig = useCallback(async () => {
    if (!config || !enabled) return;

    try {
      await getSUIServerConfig(config);
    } catch (error) {
      console.error('Failed to fetch server config:', error);
    }
  }, [config, enabled]);

  useFocusEffect(
    useCallback(() => {
      if (config && enabled) {
        fetchServerConfig();
      }
    }, [fetchServerConfig, config, enabled])
  );

  return { fetchServerConfig };
}

/**
 * 通过配置ID获取S-UI服务器配置的工具函数
 * @param configId 配置ID
 * @returns Promise<any | null> 返回服务器配置或null
 */
export async function fetchSUIServerConfigById(configId: string): Promise<any | null> {
  try {
    const store = useAppStore.getState();
    const config = store.configs.find(c => c.id === configId && c.type === 's-ui') as SUIConfig;

    if (!config) {
      console.error('S-UI config not found for ID:', configId);
      return null;
    }

    return await getSUIServerConfig(config);
  } catch (error) {
    console.error('Failed to fetch S-UI server config by ID:', error);
    return null;
  }
}