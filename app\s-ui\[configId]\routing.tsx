import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { SUIConfig } from '@/lib/types';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus, Save, RotateCcw, GripVertical, ArrowUp, ArrowDown } from 'lucide-react-native';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { ScrollView, StyleSheet, View, Alert, TouchableOpacity, SafeAreaView, RefreshControl } from 'react-native';

// 路由规则类型定义
interface RouteRule {
  id: string;
  type: string;
  domain?: string[];
  ip?: string[];
  port?: string;
  sourcePort?: string;
  network?: string;
  source?: string[];
  user?: string[];
  inboundTag?: string[];
  protocol?: string[];
  outboundTag: string;
  balancerTag?: string;
}

export default function SUIRoutingScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const { configs } = useAppStore();

  // 直接从configs中计算config
  const config = useMemo(() => {
    if (!configId) return null;
    return configs.find(c => c.id === configId) as SUIConfig || null;
  }, [configId, configs]);

  const [rules, setRules] = useState<RouteRule[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // 加载路由规则
  const loadRoutingRules = useCallback(async () => {
    if (!config) return;

    try {
      setIsLoading(true);
      // TODO: 实现S-UI路由规则获取API
      // const result = await getSUIRoutingRules(config);
      // setRules(result || []);
      
      // 临时模拟数据
      setRules([]);
      setHasChanges(false);
    } catch (error) {
      console.error('Failed to load S-UI routing rules:', error);
      Alert.alert(t('common.error'), t('sui.routing.loadFailed'));
    } finally {
      setIsLoading(false);
    }
  }, [config, t]);

  // 刷新数据
  const refreshRoutingRules = useCallback(async () => {
    setIsRefreshing(true);
    await loadRoutingRules();
    setIsRefreshing(false);
  }, [loadRoutingRules]);

  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      loadRoutingRules();
    }, [loadRoutingRules])
  );

  // 添加规则
  const handleAddRule = () => {
    // TODO: 导航到规则配置页面
    Alert.alert(t('common.info'), 'Add routing rule functionality will be implemented');
  };

  // 编辑规则
  const handleEditRule = (rule: RouteRule) => {
    // TODO: 导航到规则编辑页面
    Alert.alert(t('common.info'), `Edit rule: ${rule.outboundTag}`);
  };

  // 删除规则
  const handleDeleteRule = (rule: RouteRule) => {
    Alert.alert(
      t('sui.routing.deleteRule'),
      t('sui.routing.deleteRuleConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            const newRules = rules.filter(r => r.id !== rule.id);
            setRules(newRules);
            setHasChanges(true);
          },
        },
      ]
    );
  };

  // 移动规则
  const moveRule = (index: number, direction: 'up' | 'down') => {
    const newRules = [...rules];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    
    if (targetIndex >= 0 && targetIndex < newRules.length) {
      [newRules[index], newRules[targetIndex]] = [newRules[targetIndex], newRules[index]];
      setRules(newRules);
      setHasChanges(true);
    }
  };

  // 保存更改
  const handleSaveChanges = async () => {
    if (!config || !hasChanges) return;

    try {
      // TODO: 实现保存API
      Alert.alert(t('common.success'), t('sui.routing.saveSuccess'));
      setHasChanges(false);
    } catch (error) {
      Alert.alert(t('common.error'), t('sui.routing.saveFailed'));
    }
  };

  // 重置更改
  const handleResetChanges = () => {
    Alert.alert(
      t('sui.routing.resetChanges'),
      t('sui.routing.resetChangesConfirm'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.confirm'),
          style: 'destructive',
          onPress: () => {
            loadRoutingRules();
          },
        },
      ]
    );
  };

  // 格式化规则条件
  const formatRuleConditions = (rule: RouteRule): string => {
    const conditions: string[] = [];
    
    if (rule.domain && rule.domain.length > 0) {
      conditions.push(`Domain: ${rule.domain.slice(0, 2).join(', ')}${rule.domain.length > 2 ? '...' : ''}`);
    }
    if (rule.ip && rule.ip.length > 0) {
      conditions.push(`IP: ${rule.ip.slice(0, 2).join(', ')}${rule.ip.length > 2 ? '...' : ''}`);
    }
    if (rule.port) {
      conditions.push(`Port: ${rule.port}`);
    }
    if (rule.protocol && rule.protocol.length > 0) {
      conditions.push(`Protocol: ${rule.protocol.join(', ')}`);
    }
    
    return conditions.length > 0 ? conditions.join(' | ') : t('sui.routing.noConditions');
  };

  if (!config) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: textColor + '60' }]}>
            {t('common.configNotFound')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={refreshRoutingRules}
            tintColor={textColor}
          />
        }
      >
        {/* 头部操作栏 */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: textColor }]}>
            {t('sui.routing.title')}
          </Text>
          <View style={styles.headerActions}>
            {hasChanges && (
              <>
                <Button
                  onPress={handleResetChanges}
                  style={[styles.actionButton, { borderColor }]}
                  variant="outline"
                >
                  <RotateCcw size={16} color={textColor} />
                </Button>
                <Button
                  onPress={handleSaveChanges}
                  style={[styles.actionButton, styles.saveButton]}
                >
                  <Save size={16} color="white" />
                </Button>
              </>
            )}
            <Button
              onPress={handleAddRule}
              style={[styles.actionButton, { borderColor }]}
              variant="outline"
            >
              <Plus size={16} color={textColor} />
            </Button>
          </View>
        </View>

        {/* 路由规则列表 */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: textColor + '60' }]}>
              {t('common.loading')}
            </Text>
          </View>
        ) : rules.length === 0 ? (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: textColor + '60' }]}>
              {t('sui.routing.noRules')}
            </Text>
            <Text style={[styles.noDataSubtext, { color: textColor + '40' }]}>
              {t('sui.routing.noRulesDesc')}
            </Text>
          </View>
        ) : (
          <View style={styles.rulesList}>
            {rules.map((rule, index) => (
              <View key={rule.id} style={[styles.ruleCard, { backgroundColor, borderColor }]}>
                {/* 卡片头部 */}
                <View style={styles.cardHeader}>
                  <View style={styles.cardTitleContainer}>
                    <Text style={[styles.cardTitle, { color: textColor }]}>
                      {t('sui.routing.rule')} {index + 1}
                    </Text>
                    <Badge variant="secondary" style={styles.outboundBadge}>
                      <Text style={styles.badgeText}>
                        → {rule.outboundTag}
                      </Text>
                    </Badge>
                  </View>
                  <View style={styles.moveButtons}>
                    <TouchableOpacity
                      onPress={() => moveRule(index, 'up')}
                      disabled={index === 0}
                      style={[styles.moveButton, { opacity: index === 0 ? 0.3 : 1 }]}
                    >
                      <ArrowUp size={16} color={textColor} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => moveRule(index, 'down')}
                      disabled={index === rules.length - 1}
                      style={[styles.moveButton, { opacity: index === rules.length - 1 ? 0.3 : 1 }]}
                    >
                      <ArrowDown size={16} color={textColor} />
                    </TouchableOpacity>
                  </View>
                </View>

                {/* 卡片内容 */}
                <View style={styles.cardContent}>
                  <Text style={[styles.conditionsText, { color: textColor + '80' }]}>
                    {formatRuleConditions(rule)}
                  </Text>
                </View>

                {/* 卡片操作 */}
                <View style={styles.cardActions}>
                  <TouchableOpacity
                    onPress={() => handleEditRule(rule)}
                    style={[styles.actionButton, { borderColor }]}
                  >
                    <Text style={[styles.actionButtonText, { color: textColor }]}>
                      {t('common.edit')}
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    onPress={() => handleDeleteRule(rule)}
                    style={[styles.actionButton, styles.deleteButton]}
                  >
                    <Text style={[styles.actionButtonText, { color: '#ef4444' }]}>
                      {t('common.delete')}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  saveButton: {
    backgroundColor: '#10b981',
    borderColor: '#10b981',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  noDataText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  rulesList: {
    gap: 12,
  },
  ruleCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  outboundBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  badgeText: {
    fontSize: 12,
  },
  moveButtons: {
    flexDirection: 'row',
    gap: 4,
  },
  moveButton: {
    padding: 4,
  },
  cardContent: {
    marginBottom: 12,
  },
  conditionsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  cardActions: {
    flexDirection: 'row',
    gap: 8,
  },
  deleteButton: {
    borderColor: '#ef4444',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
