import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { SUIConfig } from '@/lib/types';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus, Settings, Globe, Shield, Eye, EyeOff } from 'lucide-react-native';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { ScrollView, StyleSheet, View, Alert, TouchableOpacity, SafeAreaView, RefreshControl } from 'react-native';

// 入站配置类型定义
interface InboundConfig {
  id: number;
  up: number;
  down: number;
  total: number;
  remark: string;
  enable: boolean;
  expiryTime: number;
  listen: string;
  port: number;
  protocol: string;
  settings: any;
  streamSettings: any;
  tag: string;
  sniffing: any;
}

export default function SUIInboundsScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const { configs } = useAppStore();

  // 直接从configs中计算config
  const config = useMemo(() => {
    if (!configId) return null;
    return configs.find(c => c.id === configId) as SUIConfig || null;
  }, [configId, configs]);

  const [inbounds, setInbounds] = useState<InboundConfig[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 加载入站配置
  const loadInbounds = useCallback(async () => {
    if (!config) return;

    try {
      setIsLoading(true);
      // TODO: 实现S-UI入站配置获取API
      // const result = await getSUIInbounds(config);
      // setInbounds(result || []);
      
      // 临时模拟数据
      setInbounds([]);
    } catch (error) {
      console.error('Failed to load S-UI inbounds:', error);
      Alert.alert(t('common.error'), t('sui.inbounds.loadFailed'));
    } finally {
      setIsLoading(false);
    }
  }, [config, t]);

  // 刷新数据
  const refreshInbounds = useCallback(async () => {
    setIsRefreshing(true);
    await loadInbounds();
    setIsRefreshing(false);
  }, [loadInbounds]);

  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      loadInbounds();
    }, [loadInbounds])
  );

  // 添加入站
  const handleAddInbound = () => {
    // TODO: 导航到入站配置页面
    Alert.alert(t('common.info'), 'Add inbound functionality will be implemented');
  };

  // 编辑入站
  const handleEditInbound = (inbound: InboundConfig) => {
    // TODO: 导航到入站编辑页面
    Alert.alert(t('common.info'), `Edit inbound: ${inbound.remark}`);
  };

  // 删除入站
  const handleDeleteInbound = (inbound: InboundConfig) => {
    Alert.alert(
      t('sui.inbounds.deleteInbound'),
      t('sui.inbounds.deleteInboundConfirm', { name: inbound.remark }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: 实现删除API
              Alert.alert(t('common.success'), t('sui.inbounds.deleteSuccess'));
              await loadInbounds();
            } catch (error) {
              Alert.alert(t('common.error'), t('sui.inbounds.deleteFailed'));
            }
          },
        },
      ]
    );
  };

  // 切换入站状态
  const toggleInboundStatus = async (inbound: InboundConfig) => {
    try {
      // TODO: 实现状态切换API
      Alert.alert(t('common.info'), `Toggle status for: ${inbound.remark}`);
    } catch (error) {
      Alert.alert(t('common.error'), t('sui.inbounds.toggleFailed'));
    }
  };

  // 格式化字节
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化到期时间
  const formatExpiryTime = (timestamp: number): string => {
    if (timestamp === 0) return t('sui.inbounds.neverExpire');
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  if (!config) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: textColor + '60' }]}>
            {t('common.configNotFound')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={refreshInbounds}
            tintColor={textColor}
          />
        }
      >
        {/* 头部操作栏 */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: textColor }]}>
            {t('sui.inbounds.title')}
          </Text>
          <Button
            onPress={handleAddInbound}
            style={[styles.addButton, { borderColor }]}
            variant="outline"
          >
            <Plus size={16} color={textColor} />
            <Text style={[styles.addButtonText, { color: textColor }]}>
              {t('sui.inbounds.addInbound')}
            </Text>
          </Button>
        </View>

        {/* 入站列表 */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: textColor + '60' }]}>
              {t('common.loading')}
            </Text>
          </View>
        ) : inbounds.length === 0 ? (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: textColor + '60' }]}>
              {t('sui.inbounds.noInbounds')}
            </Text>
            <Text style={[styles.noDataSubtext, { color: textColor + '40' }]}>
              {t('sui.inbounds.noInboundsDesc')}
            </Text>
          </View>
        ) : (
          <View style={styles.inboundsList}>
            {inbounds.map((inbound) => (
              <View key={inbound.id} style={[styles.inboundCard, { backgroundColor, borderColor }]}>
                {/* 卡片头部 */}
                <View style={styles.cardHeader}>
                  <View style={styles.cardTitleContainer}>
                    <Text style={[styles.cardTitle, { color: textColor }]}>
                      {inbound.remark || `${inbound.protocol}:${inbound.port}`}
                    </Text>
                    <Badge
                      variant={inbound.enable ? "default" : "secondary"}
                      style={styles.statusBadge}
                    >
                      <Text style={styles.badgeText}>
                        {inbound.enable ? t('common.enabled') : t('common.disabled')}
                      </Text>
                    </Badge>
                  </View>
                  <TouchableOpacity
                    onPress={() => toggleInboundStatus(inbound)}
                    style={styles.toggleButton}
                  >
                    {inbound.enable ? (
                      <Eye size={20} color={textColor} />
                    ) : (
                      <EyeOff size={20} color={textColor + '60'} />
                    )}
                  </TouchableOpacity>
                </View>

                {/* 卡片内容 */}
                <View style={styles.cardContent}>
                  <View style={styles.infoRow}>
                    <Text style={[styles.infoLabel, { color: textColor + '80' }]}>
                      {t('sui.inbounds.protocol')}:
                    </Text>
                    <Text style={[styles.infoValue, { color: textColor }]}>
                      {inbound.protocol.toUpperCase()}
                    </Text>
                  </View>
                  
                  <View style={styles.infoRow}>
                    <Text style={[styles.infoLabel, { color: textColor + '80' }]}>
                      {t('sui.inbounds.port')}:
                    </Text>
                    <Text style={[styles.infoValue, { color: textColor }]}>
                      {inbound.port}
                    </Text>
                  </View>

                  <View style={styles.infoRow}>
                    <Text style={[styles.infoLabel, { color: textColor + '80' }]}>
                      {t('sui.inbounds.traffic')}:
                    </Text>
                    <Text style={[styles.infoValue, { color: textColor }]}>
                      ↑{formatBytes(inbound.up)} ↓{formatBytes(inbound.down)}
                    </Text>
                  </View>

                  {inbound.expiryTime > 0 && (
                    <View style={styles.infoRow}>
                      <Text style={[styles.infoLabel, { color: textColor + '80' }]}>
                        {t('sui.inbounds.expiry')}:
                      </Text>
                      <Text style={[styles.infoValue, { color: textColor }]}>
                        {formatExpiryTime(inbound.expiryTime)}
                      </Text>
                    </View>
                  )}
                </View>

                {/* 卡片操作 */}
                <View style={styles.cardActions}>
                  <TouchableOpacity
                    onPress={() => handleEditInbound(inbound)}
                    style={[styles.actionButton, { borderColor }]}
                  >
                    <Settings size={16} color={textColor} />
                    <Text style={[styles.actionButtonText, { color: textColor }]}>
                      {t('common.edit')}
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    onPress={() => handleDeleteInbound(inbound)}
                    style={[styles.actionButton, styles.deleteButton]}
                  >
                    <Text style={[styles.actionButtonText, { color: '#ef4444' }]}>
                      {t('common.delete')}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  addButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  noDataText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  inboundsList: {
    gap: 12,
  },
  inboundCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  badgeText: {
    fontSize: 12,
  },
  toggleButton: {
    padding: 4,
  },
  cardContent: {
    gap: 8,
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 14,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  cardActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    gap: 4,
  },
  deleteButton: {
    borderColor: '#ef4444',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
