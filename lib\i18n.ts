import * as Localization from 'expo-localization';
import { Language } from './types';

// 翻译文本定义
export const translations = {
  en: {
    // 通用
    common: {
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      saving: 'Saving...',
      cancel: 'Cancel',
      ok: 'OK',
      confirm: 'Confirm',
      loading: 'Loading...',
      connecting: 'Connecting...',
      error: 'Error',
      success: 'Success',
      name: 'Name',
      url: 'URL',
      username: 'Username',
      password: 'Password',
      protocol: 'Protocol',
      certificate: 'Certificate',
      group: 'Group',
      all: 'All',
      default: 'Default',
      deleteConfirmMessage: 'Are you sure you want to delete this configuration?',
      deleteError: 'Failed to delete configuration',
      import: 'Import',
      importConfig: 'Import Configuration',
      importConfigFrom: 'Import Configuration From',
      selectConfigToImport: 'Select Configuration to Import',
      importSuccess: 'Configuration imported successfully',
      importError: 'Failed to import configuration',
      unsupportedType: 'Unsupported configuration type',
      scanQRCode: 'Scan QR Code',
      generateQRCode: 'Generate',
      yes: 'Yes',
      no: 'No',
      value: 'Value',
      unknownError: 'Unknown error',
      developing: 'Developing',
    },
    
    // 导航
    navigation: {
      home: 'Home',
      settings: 'Settings',
      addConfig: 'Add Configuration',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'Server List',
      addConfiguration: 'Add Configuration',
      editGroups: 'Edit Groups',
      noConfigurations: 'No configurations yet',
      addFirstConfig: 'Add your first configuration',
    },
    
    // 添加配置
    addConfig: {
      title: 'Add Configuration',
      selectType: 'Select Configuration Type',
      configName: 'Configuration Name',
      apiKey: 'API Key',
      httpWarning: 'This means API and other sensitive information will be transmitted in plain text. Please ensure you are in a secure network environment.',
      certTooltip: 'Please fill in if using self-signed certificate',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: 'Select Group',
      submit: 'Add Configuration',
    },

    // 配置表单
    configForm: {
      name: 'Configuration Name',
      namePlaceholder: 'Enter configuration name',
      protocol: 'Protocol',
      url: 'URL',
      username: 'Username',
      usernamePlaceholder: 'Enter username',
      password: 'Password',
      passwordPlaceholder: 'Enter password',
      api: 'API Key',
      apiPlaceholder: 'Enter API key',
      cert: 'Certificate',
      certTooltip: 'Please fill in if using self-signed certificate',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      publicKeyHash: 'Public Key Hash (SHA256)',
      publicKeyHashTooltip: 'Certificate pinning is only used to further enhance security when communicating with system-trusted certificates. Self-signed certificates will still fail to connect.',
      ipAddressCertWarning: 'IP addresses cannot be used for SSL certificate pinning. Please use a domain name instead.',
      publicKeyHashPlaceholder: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456\n9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba',
      group: 'Group',
      groupHint: 'Configuration will be automatically added to "All" group. You can optionally select additional groups.',
      httpWarning: 'This means API and other sensitive information will be transmitted in plain text. Please ensure you are in a secure network environment.',
      httpWarningTitle: 'Security Warning',
      connectionFailed: 'Failed to connect to the panel. Please check your configuration.',
      submitError: 'Failed to submit configuration. Please try again.',
      addSUIConfig: 'Add S-UI Configuration',
      editSUIConfig: 'Edit S-UI Configuration',
      addXUIConfig: 'Add X-UI Configuration',
      editXUIConfig: 'Edit X-UI Configuration',
      add3XUIConfig: 'Add 3X-UI Configuration',
      edit3XUIConfig: 'Edit 3X-UI Configuration',
    },

    // 验证
    validation: {
      required: 'This field is required',
      invalidUrl: 'Please enter a valid URL',
    },
    
    // 分组管理
    groups: {
      title: 'Manage Groups',
      editGroups: 'Edit Groups',
      all: 'All',
      addGroup: 'Add Group',
      rename: 'Rename',
      moveUp: 'Move Up',
      moveDown: 'Move Down',
      deleteGroup: 'Delete Group',
      deleteConfirm: 'Are you sure you want to delete this group? All configurations in this group will be moved to the all group.',
      groupName: 'Group Name',
      enterGroupName: 'Enter group name',
      renameGroup: 'Rename Group',
      newGroupName: 'New group name',
      cannotDeleteDefault: 'Cannot delete all group',
      cannotRenameDefault: 'Cannot rename all group',
    },

    // 3X-UI 专用翻译
    threeXUI: {
      // Drawer 导航
      drawer: {
        overview: 'Overview',
        inbounds: 'Inbounds',
        routing: 'Routing Rules',
        outbounds: 'Outbounds',
        backToHome: 'Back to Home',
      },

      // 概述页面
      overview: {
        title: '3X-UI Overview',
        loading: 'Loading...',
        xrayStatus: 'Xray Status',
        restart: 'Restart',
        restarting: 'Restarting...',
        update: 'Update',
        version: 'Version',
        systemResources: 'System Resources',
        cpu: 'CPU',
        memory: 'Memory',
        disk: 'Disk',
        networkTraffic: 'Network Traffic',
        selectXrayVersion: 'Select Xray Version',
        install: 'Install',
        installing: 'Installing...',
        serverOffline: 'Server offline or connection failed',
        gettingData: 'Getting server data...',
        restartSuccess: 'Xray service restarted successfully',
        restartFailed: 'Xray service restart failed',
        installSuccess: 'Xray {version} installed successfully',
        installFailed: 'Xray installation failed',
        confirmRestart: 'Confirm Restart',
        confirmRestartMessage: 'Are you sure you want to restart the Xray service? This will apply new routing rule configurations.',
        ipCopySuccess: '{version} address copied to clipboard',
        ipCopyFailed: 'Failed to copy IP address',
      },

      // 路由规则
      routing: {
        title: 'Routing Rules',
        add: 'Add',
        save: 'Save',
        restart: 'Restart',
        updateGeo: 'Update Geo',
        updating: 'Updating {percentage}%',
        noRules: 'No routing rules',
        noRulesSubtext: 'Click the add button above to create the first routing rule',
        editRule: 'Edit Rule',
        deleteRule: 'Delete Rule',
        confirmDelete: 'Confirm Delete',
        confirmDeleteMessage: 'Are you sure you want to delete this routing rule? This action cannot be undone.',
        confirmRestart: 'Confirm Restart',
        confirmRestartMessage: 'Are you sure you want to restart the Xray service? This will apply new routing rule configurations.',
        confirmUpdateGeo: 'Confirm Update',
        confirmUpdateGeoMessage: 'Are you sure you want to update the geo database? This will download the latest geosite and geoip data.',
        updateGeoSuccess: 'Update completed!',
        updateGeoFailed: 'Update failed',
        saveSuccess: 'Routing rules saved',
        saveFailed: 'Save failed',
        loadFailed: 'Failed to load routing configuration',
        restartSuccess: 'Xray service restarted successfully',
        restartFailed: 'Xray service restart failed',
        updateFailed: 'Update failed',
        unknownError: 'Unknown error',
        ruleDescription: {
          domain: 'Domain',
          ip: 'IP',
          port: 'Port',
          network: 'Network',
          protocol: 'Protocol',
          inbound: 'Inbound',
          emptyRule: 'Empty rule',
          rule: 'Rule {number}',
        },
      },

      // 入站配置
      inbounds: {
        title: 'Inbound List',
        add: 'Add',
        userManagement: 'User Management',
        noInbounds: 'No inbound configurations',
        noInboundsSubtext: 'Click the button above to add the first inbound configuration',
        editConfig: 'Edit Configuration',
        exportLinks: 'Export Links',
        resetTraffic: 'Reset Traffic',
        exportJson: 'Export JSON',
        enable: 'Enable',
        disable: 'Disable',
        deleteConfig: 'Delete Configuration',
        confirmDelete: 'Confirm Delete',
        confirmDeleteMessage: 'Are you sure you want to delete this inbound configuration? This action cannot be undone.',
        confirmResetTraffic: 'Confirm Reset',
        confirmResetTrafficMessage: 'Are you sure you want to reset the traffic statistics for this inbound configuration?',
        configNotFound: 'Configuration not found',
        loadFailed: 'Failed to load inbound list',
        enableSuccess: 'Configuration enabled',
        disableSuccess: 'Configuration disabled',
        operationFailed: 'Operation failed',
        resetSuccess: 'Traffic reset',
        resetFailed: 'Reset failed',
        exportSuccess: 'Configuration JSON copied to clipboard',
        exportFailed: 'Export failed',
        deleteSuccess: 'Configuration deleted',
        deleteFailed: 'Delete failed',
        userManagementComingSoon: 'User management feature coming soon',
        status: {
          valid: 'Valid',
          invalid: 'Invalid',
        },
      },

      // 路由规则配置
      ruleConfig: {
        title: 'Add Routing Rule',
        editTitle: 'Edit Routing Rule',
        domainMatcher: 'Domain Matcher',
        domain: 'Domain',
        domainPlaceholder: 'Enter domains, separated by commas',
        ip: 'IP Address',
        ipPlaceholder: 'Enter IP addresses or CIDR, separated by commas',
        port: 'Port',
        portPlaceholder: 'e.g.: 53,443,1000-2000',
        sourcePort: 'Source Port',
        sourcePortPlaceholder: 'e.g.: 53,443,1000-2000',
        network: 'Network Type',
        source: 'Source Address',
        sourcePlaceholder: 'Enter source IP addresses, separated by commas',
        user: 'User',
        userPlaceholder: 'Enter user emails, separated by commas',
        inboundTag: 'Inbound Tag',
        protocol: 'Protocol',
        httpAttrs: 'HTTP Attributes',
        attrKeyPlaceholder: ':method',
        attrValuePlaceholder: 'GET',
        outboundTag: 'Outbound Tag',
        balancerTag: 'Balancer Tag',
        balancerTagPlaceholder: 'Enter balancer tag',
        saveRule: 'Save Rule',
        configNotFound: 'Configuration information not found',
        saveSuccess: 'Routing rule added',
        updateSuccess: 'Routing rule updated',
        saveFailed: 'Save failed',
      },

      // 导出链接
      exportLinks: {
        title: 'Export Links',
        noLinks: 'No available user links',
        close: 'Close',
        copySuccess: 'Link for {label} copied to clipboard',
        copyFailed: 'Copy failed',
        generateFailed: 'Failed to generate links',
        user: 'User {number}',
      },

      // 出站配置
      outbounds: {
        title: 'Outbound List',
        add: 'Add',
        restart: 'Restart',
        restarting: 'Restarting...',
        noOutbounds: 'No outbound configurations',
        noOutboundsSubtext: 'Click the add button above to create a new outbound configuration',
        editConfig: 'Edit Configuration',
        deleteConfig: 'Delete Configuration',
        confirmDelete: 'Confirm Delete',
        confirmDeleteMessage: 'Are you sure you want to delete this outbound configuration? This action cannot be undone.',
        confirmRestart: 'Confirm Restart',
        confirmRestartMessage: 'Are you sure you want to restart the Xray service? This will apply new outbound configurations.',
        loadFailed: 'Failed to load outbound list',
        deleteSuccess: 'Outbound configuration deleted',
        deleteFailed: 'Delete failed',
        restartSuccess: 'Xray service restarted successfully',
        restartFailed: 'Xray service restart failed',
        outboundName: 'Outbound-{number}',

        // WARP配置
        warp: {
          title: 'WARP Configuration',
          addWarp: 'Add WARP',
          addWarpMessage: 'No WARP configuration found. Would you like to add one now?',
          addOutbound: 'Add Outbound',
          delete: 'Delete',
          confirmDeleteWarp: 'Confirm Delete',
          confirmDeleteWarpMessage: 'Are you sure you want to delete the WARP configuration? This action cannot be undone.',
          deleteWarpSuccess: 'WARP configuration deleted',
          deleteWarpFailed: 'Failed to delete WARP configuration',
          getWarpFailed: 'Failed to get WARP configuration',
          warpExists: 'WARP outbound configuration already exists',
          name: 'Name:',
          type: 'Type:',
          accountType: 'Account Type:',
          ipv4Address: 'IPv4 Address:',
          ipv6Address: 'IPv6 Address:',
          endpoint: 'Endpoint:',
          expiryTime: 'Expiry Time:',
          unknown: 'Unknown',
        },
      },

      // 入站配置表单
      inboundConfig: {
        addTitle: 'Add Inbound Configuration',
        editTitle: 'Edit Inbound Configuration',
        save: 'Save Configuration',
        update: 'Update Configuration',
        importFromClipboard: 'Import from Clipboard',
        importSuccess: 'Configuration imported from clipboard',
        importError: 'Import failed',
        importErrorEmpty: 'Clipboard is empty',
        importErrorInvalid: 'Clipboard content is not a valid inbound configuration',
        importErrorFormat: 'Clipboard content is not valid JSON format',

        // 基本设置
        protocol: 'Protocol',
        protocolPlaceholder: 'Select protocol',
        listen: 'Listen',
        listenPlaceholder: 'Leave empty for 0.0.0.0',
        port: 'Port',
        portPlaceholder: 'Required, e.g. 1080',
        expiryTime: 'Expiry Time (Days)',
        expiryTimePlaceholder: 'Leave empty for permanent',
        totalTraffic: 'Total Traffic (GB)',
        totalTrafficPlaceholder: 'Leave empty for unlimited',

        // 协议选项
        protocols: {
          vmess: 'VMess',
          vless: 'VLESS',
          trojan: 'Trojan',
          shadowsocks: 'Shadowsocks',
          dokodemo: 'Dokodemo-door',
          socks: 'SOCKS',
          http: 'HTTP',
          wireguard: 'WireGuard',
        },

        // 用户管理
        userManagement: 'User Management',
        addUser: 'Add User',
        noUsers: 'No users yet, click the button above to add users',
        email: 'Email',
        emailPlaceholder: 'Enter email',
        uuid: 'UUID',
        uuidPlaceholder: 'Enter UUID',
        password: 'Password',
        passwordPlaceholder: 'Enter password',
        enableXtlsFlow: 'Enable XTLS Flow',
        flow: 'Flow',

        // 回落管理
        fallbackManagement: 'Fallback Management',
        addFallback: 'Add Fallback',
        noFallbacks: 'No fallback configurations, click the button above to add fallback',
        fallbackName: 'Name',
        fallbackNamePlaceholder: 'Enter fallback name',
        fallbackDefaultName: 'Fallback',
        fallbackAlpn: 'ALPN',
        fallbackAlpnPlaceholder: 'Enter ALPN',
        fallbackPath: 'Path',
        fallbackPathPlaceholder: 'Enter path',
        fallbackDest: 'Destination',
        fallbackDestPlaceholder: 'Enter destination port',
        fallbackXver: 'Xver',
        fallbackXverPlaceholder: 'Enter xver value',

        // Shadowsocks 设置
        shadowsocksSettings: 'Shadowsocks Settings',
        encryptionMethod: 'Encryption Method',
        encryptionMethodPlaceholder: 'Select encryption method',
        networkType: 'Network Type',
        networkTypePlaceholder: 'Select network type',

        // HTTP 设置
        httpSettings: 'HTTP Settings',
        useAuth: 'Use Authentication',
        username: 'Username',
        usernamePlaceholder: 'Username',
        allowTransparent: 'Allow Transparent Proxy',

        // SOCKS 设置
        socksSettings: 'SOCKS Settings',
        enableUdp: 'Enable UDP',
        ipAddress: 'IP Address',

        // Dokodemo-door 设置
        dokodemoSettings: 'Dokodemo-door Settings',
        targetAddress: 'Target Address',
        targetAddressPlaceholder: '*******',
        targetPort: 'Target Port',
        targetPortPlaceholder: '53',
        followRedirect: 'Follow Redirect',

        // WireGuard 设置
        wireguardSettings: 'WireGuard Settings',
        privateKey: 'Private Key',
        privateKeyPlaceholder: 'Private key',
        publicKey: 'Public Key',
        publicKeyPlaceholder: 'Public key',
        mtu: 'MTU',
        mtuPlaceholder: '1420',
        peerManagement: 'Peer Management',
        addPeer: 'Add Peer',
        noPeers: 'No peers yet, click the button above to add peers',
        allowedIPs: 'Allowed IPs',
        allowedIPsPlaceholder: '0.0.0.0/0, ::/0',

        // 传输设置
        transportSettings: 'Transport Settings',
        transportType: 'Transport Type',
        edit: 'Edit',
        tcpHeaderType: 'TCP Header Type',
        tcpHeaderTypePlaceholder: 'Select TCP header type',

        // 安全设置
        securitySettings: 'Security Settings',
        securityType: 'Security Type',
        securityTypePlaceholder: 'Select security type',

        // SockOpt 设置
        sockOptSettings: 'SockOpt Settings',
        sockOptEnabled: 'SockOpt: Enabled',
        sockOptDisabled: 'SockOpt Disabled',

        // Sniffing 设置
        sniffingSettings: 'Sniffing Settings',
        sniffingEnabled: 'Sniffing: Enabled',
        sniffingDisabled: 'Sniffing Disabled',
        destOverride: 'Destination Override',
        metadataOnly: 'Metadata Only',
        routeOnly: 'Route Only',
        excludedDomains: 'Excluded Domains',

        // 错误消息
        errorProtocolRequired: 'Please select a protocol',
        errorPortRequired: 'Please enter a port',
        errorPortInvalid: 'Port must be a number between 1-65535',
        errorEmailRequired: 'Please enter email',
        errorUuidRequired: 'Please enter UUID',
        errorPasswordRequired: 'Please enter password',
        errorPublicKeyRequired: 'Please enter public key',
        errorConfigNotFound: 'Configuration not found',
        errorServerConfigNotFound: 'Server configuration not found',
        errorSaveFailed: 'Failed to save configuration, please check network connection',
        errorGenerateKeysFailed: 'Failed to generate keys',
        errorCertPathRequired: 'Please enter certificate file path and key file path',
        errorCertContentRequired: 'Please enter certificate content and key content',

        // 成功消息
        successConfigAdded: 'Configuration added',
        successConfigUpdated: 'Configuration updated',

        // 确认对话框
        confirmDelete: 'Confirm Delete',
        confirmDeleteUser: 'Are you sure you want to delete user {email}?',
        confirmDeleteFallback: 'Are you sure you want to delete this fallback configuration?',
        confirmDeletePeer: 'Are you sure you want to delete this peer?',
        confirmDeleteCert: 'Are you sure you want to delete this certificate?',

        // TLS 设置
        tlsSettings: 'TLS Settings',
        rejectUnknownSni: 'Reject Unknown SNI',
        allowInsecure: 'Allow Insecure Connections',
        disableSystemRoot: 'Disable System Root Certificates',
        enableSessionResumption: 'Enable Session Resumption',
        alpn: 'ALPN',
        minVersion: 'Min Version',
        maxVersion: 'Max Version',
        certificates: 'Certificates',
        addCertificate: 'Add Certificate',
        noCertificates: 'No certificates configured',

        // Reality 设置
        realitySettings: 'Reality Settings',
        target: 'Target',
        targetPlaceholder: 'example.com:443',
        serverNames: 'Server Names',
        serverNamesPlaceholder: 'example.com,www.example.com',
        shortIds: 'Short IDs',
        shortIdsPlaceholder: 'Short ID list, separated by commas',
        maxTimeDiff: 'Max Time Diff',

        // 传输协议设置
        acceptProxyProtocol: 'Accept Proxy Protocol',
        httpMasquerading: 'HTTP Masquerading',
        httpMasqueradingSettings: 'HTTP Masquerading Settings',
        masqueradingType: 'Masquerading Type',
        masqueradingTypePlaceholder: 'Select masquerading type',

        // TCP 设置
        tcpSettings: 'TCP Settings',

        // XHTTP 设置
        xhttpSettings: 'XHTTP Settings',
        host: 'Host',
        hostPlaceholder: 'xray.com',
        path: 'Path',
        pathPlaceholder: '/path',
        mode: 'Mode',

        // mKCP 设置
        mkcpSettings: 'mKCP Settings',
        mkcpMtu: 'MTU',
        mkcpMtuPlaceholder: '1350',
        tti: 'TTI',
        ttiPlaceholder: '20',
        uplinkCapacity: 'Uplink Capacity',
        uplinkCapacityPlaceholder: '5',
        downlinkCapacity: 'Downlink Capacity',
        downlinkCapacityPlaceholder: '20',
        congestion: 'Congestion',
        readBufferSize: 'Read Buffer Size',
        writeBufferSize: 'Write Buffer Size',

        // gRPC 设置
        grpcSettings: 'gRPC Settings',
        authority: 'Authority',
        authorityPlaceholder: 'grpc.example.com',
        serviceName: 'Service Name',
        serviceNamePlaceholder: 'name',
        initialWindowsSize: 'Initial Windows Size',
        initialWindowsSizePlaceholder: '0',

        // WebSocket 设置
        websocketSettings: 'WebSocket Settings',
        heartbeatPeriod: 'Heartbeat Period',
        heartbeatPeriodPlaceholder: '10',

        // HTTP Upgrade 设置
        httpUpgradeSettings: 'HTTP Upgrade Settings',

        // HTTP 伪装详细设置
        requestConfig: 'Request Configuration',
        responseConfig: 'Response Configuration',
        httpRequestVersion: 'Version',
        httpRequestVersionPlaceholder: '1.1',
        httpRequestMethod: 'Method',
        httpRequestMethodPlaceholder: 'GET',
        requestPath: 'Request Path',
        requestHeaders: 'Request Headers',
        responseHeaders: 'Response Headers',
        httpResponseStatus: 'Status',
        httpResponseStatusPlaceholder: '200',
        httpResponseReason: 'Reason',
        httpResponseReasonPlaceholder: 'OK',
        addHeader: 'Add Header',
        headerKey: 'Key',
        headerValue: 'Value',
        headerKeyPlaceholder: 'Host',
        headerValuePlaceholder: 'Value',
        customHeaders: 'Custom Headers',
        noHeaders: 'No headers configured',

        // XHTTP 额外设置
        xPaddingBytes: 'X-Padding Bytes',
        xPaddingBytesPlaceholder: '100-1000',
        noSSEHeader: 'No SSE Header',
        scMaxEachPostBytes: 'SC Max Each Post Bytes',
        scMaxEachPostBytesPlaceholder: '1000000',
        scMaxConcurrentPosts: 'SC Max Concurrent Posts',
        scMaxConcurrentPostsPlaceholder: '100',
        scMinPostsIntervalMs: 'SC Min Posts Interval (ms)',
        scMinPostsIntervalMsPlaceholder: '30',

        // 传输详情显示
        modeLabel: 'Mode',
        headersCount: 'Headers',
        headersCountSuffix: '',
        hostLabel: 'Host',
        pathLabel: 'Path',
        notSet: 'Not set',
        domainStrategy: 'Domain Strategy',

        // 路径管理
        httpRequestPath: 'Request Path',
        addPath: 'Add Path',

        // 伪装设置
        masqueradingDomain: 'Masquerading Domain',
        masqueradingDomainPlaceholder: 'example.com',

        // mKCP 种子密钥
        seedKey: 'Seed Key',
        seedKeyPlaceholder: 'Password',

        // Reality 密钥占位符
        realityPrivateKeyPlaceholder: 'Private Key',
        realityPublicKeyPlaceholder: 'Public Key',
        realityPublicKeyLabel: 'Public Key',

        // 证书密钥内容
        certKeyContentLabel: 'Key Content',
        certKeyContentPlaceholder: '-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----',

        // TLS 版本和协议设置
        alpnProtocol: 'ALPN Protocol',
        selectVersion: 'Select version',

        // TLS 版本选项
        tlsVersion10: '1.0',
        tlsVersion11: '1.1',
        tlsVersion12: '1.2',
        tlsVersion13: '1.3',

        // 证书相关
        certificateContent: 'Certificate Content',
        certificateContentPlaceholder: '-----BEGIN CERTIFICATE-----\\n...\\n-----END CERTIFICATE-----',
        certificateCount: 'Certificates',
        certificateCountSuffix: '',

        // 选择占位符和提示
        selectMode: 'Select mode',
        selectXver: 'Select Xver',
        selectTproxyMode: 'Select TProxy mode',
        destOverrideLabel: 'Destination Override (destOverride)',

        // 伪装类型选项
        masqueradingNone: 'None',

        // 错误消息
        errorConfigIdNotFound: 'Configuration ID not found',
        errorOriginalConfigNotFound: 'Original configuration not found',

        // Sniffing 设置
        metadataOnlyLabel: 'Metadata Only',
        routeOnlyLabel: 'Route Only (routeOnly)',

        // 限制设置
        limitFallbackUpload: 'Limit Fallback Upload',
        limitFallbackDownload: 'Limit Fallback Download',
        afterBytes: 'After Bytes',
        afterBytesPlaceholder: '0',
        bytesPerSec: 'Bytes Per Sec',
        bytesPerSecPlaceholder: '0',
        burstBytesPerSec: 'Burst Bytes Per Sec',
        burstBytesPerSecPlaceholder: '0',

        // 底部弹窗标题
        sockOptSettingsTitle: 'SockOpt Settings',
        sniffingSettingsTitle: 'Sniffing Settings',
        certificateSettingsTitle: 'Certificate Settings',

        // 证书管理
        editCertificateTitle: 'Edit Certificate',
        addCertificateTitle: 'Add Certificate',
        certAddMethod: 'Add Method',
        certFileContent: 'File Content',
        certFilePath: 'File Path',
        certificateFilePath: 'Certificate File Path',
        keyFilePath: 'Key File Path',

        // 域名排除
        domainsExcluded: 'Domains Excluded',
        domainsExcludedPlaceholder: 'Enter domains to exclude, separated by commas',

        // 选择占位符
        selectDomainStrategy: 'Select domain strategy',
        selectAddressPortStrategy: 'Select address port strategy',

        // 提示信息
        realityProtocolNote: 'Note: Only VLESS and Trojan protocols support Reality',
        tcpFastOpenEnabled: 'TCP Fast Open: Enabled',

        // 按钮文本
        cancel: 'Cancel',
        delete: 'Delete',
        generate: 'Generate',
        yes: 'Yes',
        no: 'No',
      },

      // 出站配置表单
      outboundConfig: {
        addTitle: 'Add Outbound Configuration',
        editTitle: 'Edit Outbound Configuration',
        save: 'Save Configuration',
        importFromClipboard: 'Import from Clipboard',
        importSuccess: 'Protocol link imported successfully',
        importError: 'Import failed',
        importErrorEmpty: 'Clipboard is empty',
        importErrorUnsupported: 'Clipboard content is not a supported protocol link',
        parseError: 'Failed to parse Wireguard link',

        // 基本设置
        basicSettings: 'Basic Settings',
        tag: 'Tag',
        tagPlaceholder: 'Enter outbound tag',
        protocol: 'Protocol',
        protocolPlaceholder: 'Select protocol',
        sendThrough: 'Send Through',
        sendThroughPlaceholder: 'Local IP address (optional)',

        // 协议选项
        protocols: {
          freedom: 'Freedom',
          blackhole: 'Blackhole',
          dns: 'DNS',
          http: 'HTTP',
          socks: 'SOCKS',
          shadowsocks: 'Shadowsocks',
          vless: 'VLESS',
          vmess: 'VMess',
          trojan: 'Trojan',
          wireguard: 'Wireguard',
        },

        // Freedom 设置
        freedomSettings: 'Freedom Settings',
        domainStrategy: 'Domain Strategy',
        domainStrategyPlaceholder: 'Select domain strategy',
        proxyProtocolVersion: 'Proxy Protocol Version',
        redirectAddress: 'Redirect Address',
        redirectAddressPlaceholder: '127.0.0.1:3366',

        // Fragment 设置
        fragmentSettings: 'Fragment Settings',
        fragmentEnabled: 'Fragment Enabled',
        fragmentDisabled: 'Fragment Disabled',
        fragmentPackets: 'Packet Type',
        fragmentLength: 'Length Range',
        fragmentInterval: 'Interval',

        // Noises 设置
        noisesSettings: 'Noises Settings',
        addNoise: 'Add Noise',
        noNoises: 'No noise configurations. Click the button above to add noise.',
        noiseType: 'Type',
        noiseDelay: 'Delay',
        noisePacket: 'Packet',

        // Blackhole 设置
        blackholeSettings: 'Blackhole Settings',
        responseType: 'Response Type',
        responseNone: 'No Response',
        responseHttp: 'HTTP Response',

        // DNS 设置
        dnsSettings: 'DNS Settings',
        networkType: 'Network Type',
        nonIPQuery: 'Non-IP Query',
        dnsServer: 'DNS Server Address',
        dnsServerPlaceholder: '*******',
        port: 'Port',
        portPlaceholder: '53',
        drop: 'Drop',
        skip: 'Skip',

        // HTTP 设置
        httpSettings: 'HTTP Settings',
        serverAddress: 'Server Address',
        serverAddressPlaceholder: 'proxy.example.com',
        serverPort: 'Server Port',
        serverPortPlaceholder: '3128',
        username: 'Username',
        usernamePlaceholder: 'Enter username',
        password: 'Password',
        passwordPlaceholder: 'Enter password',

        // SOCKS 设置
        socksSettings: 'SOCKS Settings',
        socksPortPlaceholder: '1080',

        // Shadowsocks 设置
        shadowsocksSettings: 'Shadowsocks Settings',
        method: 'Encryption Method',
        methodPlaceholder: 'Select encryption method',
        ssPortPlaceholder: '8388',
        uot: 'UDP over TCP',
        uotVersion: 'UoT Version',

        // VLESS 设置
        vlessSettings: 'VLESS Settings',
        userId: 'User ID',
        userIdPlaceholder: 'Enter user ID',
        flow: 'Flow',
        flowPlaceholder: 'xtls-rprx-vision (optional)',
        vlessPortPlaceholder: '443',

        // VMess 设置
        vmessSettings: 'VMess Settings',
        security: 'Security',
        securityPlaceholder: 'Select security method',
        vmessPortPlaceholder: '443',

        // Trojan 设置
        trojanSettings: 'Trojan Settings',
        trojanPortPlaceholder: '443',

        // Wireguard 设置
        wireguardSettings: 'Wireguard Settings',
        secretKey: 'Secret Key',
        secretKeyPlaceholder: 'Enter private key',
        endpoint: 'Endpoint',
        endpointPlaceholder: 'server.example.com:51820',
        publicKey: 'Public Key',
        publicKeyPlaceholder: 'Enter public key',
        mtu: 'MTU',
        mtuPlaceholder: '1420',
        address: 'Address',
        addressPlaceholder: '********/32, fd00::2/128',
        noKernelTun: 'No Kernel TUN',
        reserved: 'Reserved',
        reservedPlaceholder: '0,0,0',
        workers: 'Workers',
        workersPlaceholder: 'Number of workers',
        wgDomainStrategy: 'Domain Strategy',

        // 传输设置
        transportSettings: 'Transport Settings',
        transportType: 'Transport Type',
        transportEnabled: 'Transport: Enabled',
        transportDisabled: 'Transport Disabled',
        edit: 'Edit',

        // TCP 设置
        tcpSettings: 'TCP Settings',
        headerType: 'Header Type',
        httpVersion: 'HTTP Version',
        httpMethod: 'HTTP Method',
        httpPaths: 'HTTP Paths',
        httpHeaders: 'HTTP Headers',
        responseVersion: 'Response Version',
        responseStatus: 'Response Status',
        responseReason: 'Response Reason',

        // XHTTP 设置
        xhttpSettings: 'XHTTP Settings',
        host: 'Host',
        hostPlaceholder: 'example.com',
        path: 'Path',
        pathPlaceholder: '/',
        mode: 'Mode',
        headers: 'Headers',
        paddingBytes: 'Padding Bytes',
        noGRPCHeader: 'No gRPC Header',
        minPostsInterval: 'Min Posts Interval',
        maxConcurrency: 'Max Concurrency',
        maxConnections: 'Max Connections',
        maxReuseTimes: 'Max Reuse Times',
        maxRequestTimes: 'Max Request Times',
        maxReusableSecs: 'Max Reusable Seconds',
        keepAlivePeriod: 'Keep Alive Period',
        downloadAddress: 'Download Address',
        downloadPort: 'Download Port',
        downloadNetwork: 'Download Network',

        // mKCP 设置
        mkcpSettings: 'mKCP Settings',
        tti: 'TTI',
        uplinkCapacity: 'Uplink Capacity',
        downlinkCapacity: 'Downlink Capacity',
        congestion: 'Congestion',
        readBufferSize: 'Read Buffer Size',
        writeBufferSize: 'Write Buffer Size',
        domain: 'Domain',
        seed: 'Seed',

        // gRPC 设置
        grpcSettings: 'gRPC Settings',
        authority: 'Authority',
        serviceName: 'Service Name',
        userAgent: 'User Agent',
        multiMode: 'Multi Mode',
        idleTimeout: 'Idle Timeout',
        healthCheckTimeout: 'Health Check Timeout',
        permitWithoutStream: 'Permit Without Stream',
        initialWindowsSize: 'Initial Windows Size',

        // WebSocket 设置
        websocketSettings: 'WebSocket Settings',
        heartbeatPeriod: 'Heartbeat Period',

        // HTTP Upgrade 设置
        httpUpgradeSettings: 'HTTP Upgrade Settings',

        // 安全设置
        securitySettings: 'Security Settings',
        securityType: 'Security Type',
        securityEnabled: 'Security: Enabled',
        securityDisabled: 'Security Disabled',

        // TLS 设置
        tlsSettings: 'TLS Settings',
        serverName: 'Server Name',
        serverNamePlaceholder: 'example.com',
        allowInsecure: 'Allow Insecure',
        alpn: 'ALPN',
        fingerprint: 'Fingerprint',
        pinnedPeerCerts: 'Pinned Peer Certificates',
        pinnedPeerCertsPlaceholder: 'SHA256 hashes separated by commas',

        // Reality 设置
        realitySettings: 'Reality Settings',
        realityPassword: 'Password',
        shortId: 'Short ID',
        spiderX: 'Spider X',

        // Mux 设置
        muxSettings: 'Mux Settings',
        muxEnabled: 'Mux: Enabled',
        muxDisabled: 'Mux Disabled',
        concurrency: 'Concurrency',
        xudpConcurrency: 'XUDP Concurrency',
        xudpProxyUDP443: 'XUDP Proxy UDP 443',
        reject: 'Reject',
        allow: 'Allow',

        // SockOpt 设置
        sockOptSettings: 'SockOpt Settings',
        sockOptEnabled: 'SockOpt: Enabled',
        sockOptDisabled: 'SockOpt Disabled',
        mark: 'Mark',
        tcpMaxSeg: 'TCP Max Seg',
        tcpFastOpen: 'TCP Fast Open',
        tproxy: 'TProxy',
        dialerProxy: 'Dialer Proxy',
        dialerProxyPlaceholder: 'Enter proxy tag',
        tcpKeepAliveInterval: 'TCP Keep Alive Interval',
        tcpKeepAliveIdle: 'TCP Keep Alive Idle',
        tcpUserTimeout: 'TCP User Timeout',
        tcpCongestion: 'TCP Congestion',
        interface: 'Interface',
        v6Only: 'V6 Only',
        tcpWindowClamp: 'TCP Window Clamp',
        addressPortStrategy: 'Address Port Strategy',

        // 代理设置
        proxySettings: 'Proxy Settings',
        proxyTag: 'Proxy Tag',
        proxyTagPlaceholder: 'Enter proxy tag',

        // 通用
        none: 'None',
        auto: 'Auto',
        tcp: 'TCP',
        udp: 'UDP',
        websocket: 'WebSocket',
        http2: 'HTTP/2',
        grpc: 'gRPC',
        xhttp: 'XHTTP',
        mkcp: 'mKCP',
        httpupgrade: 'HTTP Upgrade',
        tls: 'TLS',
        reality: 'Reality',
        version0: 'Version 0',
        version1: 'Version 1',
        version2: 'Version 2',
        off: 'Off',
        redirect: 'Redirect',
        tun: 'TUN',
        original: 'Original',

        // 错误消息
        saveError: 'Failed to save configuration',
        saveSuccess: 'Configuration saved successfully',
        validationError: 'Please fill in all required fields',
        tagRequired: 'Tag is required',
        serverAddressRequired: 'Server address is required',
        serverPortRequired: 'Server port is required',
        userIdRequired: 'User ID is required',
        passwordRequired: 'Password is required',
        secretKeyRequired: 'Secret key is required',
        publicKeyRequired: 'Public key is required',
        endpointRequired: 'Endpoint is required',


      },
    },

    // 设置
    settings: {
      title: 'Settings',
      theme: 'Theme',
      language: 'Language',
      proPlan: 'Pro Plan',
      subscribeToPro: 'Subscribe to Pro',
      messengerGroup: 'Messenger Group',
      proActive: 'Pro subscription is active',
      proDescription: 'Upgrade to Pro to add unlimited configurations',
      configLimit: 'Configuration limit',
      configLimitReached: 'You have reached the configuration limit. Upgrade to Pro to add more.',
      configLimitError: 'Configuration limit reached. Upgrade to Pro to add more configurations.',
      restorePurchases: 'Restore Purchases',
      purchaseError: 'Purchase failed. Please try again.',
      restoreSuccess: 'Purchases restored successfully',
      restoreError: 'Failed to restore purchases',
      openLinkError: 'Failed to open link',
      privacyPolicy: 'Privacy Policy',
      clearData: 'Clear All Data',
      clearDataConfirmMessage: 'This will remove all app data, including configurations, groups, settings and cached statuses. This action cannot be undone. Continue?',
      clearDataSuccess: 'All data has been cleared',
      clearDataError: 'Failed to clear data',
      themes: {
        light: 'Light',
        dark: 'Dark',
        system: 'Follow System',
      },
      languages: {
        en: 'English',
        'zh-CN': 'Simplified Chinese',
        'zh-TW': 'Traditional Chinese',
        fa: 'Persian',
      },
    },
  },
  
  'zh-CN': {
    // 通用
    common: {
      add: '添加',
      edit: '编辑',
      delete: '删除',
      save: '保存',
      saving: '保存中...',
      cancel: '取消',
      ok: '确定',
      confirm: '确认',
      loading: '加载中...',
      connecting: '连接中...',
      error: '错误',
      success: '成功',
      name: '名称',
      url: '地址',
      username: '用户名',
      password: '密码',
      protocol: '协议',
      certificate: '证书',
      group: '分组',
      all: '全部',
      default: '默认',
      deleteConfirmMessage: '确定要删除此配置吗？',
      deleteError: '删除配置失败',
      import: '导入',
      importConfig: '导入配置',
      importConfigFrom: '从配置导入',
      selectConfigToImport: '选择要导入的配置',
      importSuccess: '配置导入成功',
      importError: '配置导入失败',
      unsupportedType: '不支持的配置类型',
      scanQRCode: '扫描二维码',
      generateQRCode: '生成',
      yes: '是',
      no: '否',
      value: '值',
      unknownError: '未知错误',
      developing: '开发中',
    },
    
    // 导航
    navigation: {
      home: '主页',
      settings: '设置',
      addConfig: '添加配置',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: '服务器列表',
      addConfiguration: '添加配置',
      editGroups: '编辑分组',
      noConfigurations: '暂无配置',
      addFirstConfig: '添加您的第一个配置',
    },
    
    // 添加配置
    addConfig: {
      title: '添加配置',
      selectType: '选择配置类型',
      configName: '配置名称',
      apiKey: 'API 密钥',
      httpWarning: '这意味着 API 等敏感信息以明文传输，请确保在安全的网络环境中使用。',
      certTooltip: '如果为自签证书请填写',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: '选择分组',
      submit: '添加配置',
    },

    // 配置表单
    configForm: {
      name: '配置名称',
      namePlaceholder: '请输入配置名称',
      protocol: '协议',
      url: '地址',
      username: '用户名',
      usernamePlaceholder: '请输入用户名',
      password: '密码',
      passwordPlaceholder: '请输入密码',
      api: 'API 密钥',
      apiPlaceholder: '请输入 API 密钥',
      cert: '证书',
      certTooltip: '如果为自签证书请填写',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      publicKeyHash: '公钥哈希 (SHA256)',
      publicKeyHashTooltip: '固定证书仅用于在与系统信任证书通信时进一步加强安全性，自签证书仍然会连接失败',
      ipAddressCertWarning: 'IP地址无法用于SSL证书固定，请使用域名。',
      publicKeyHashPlaceholder: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456\n9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba',
      group: '分组',
      groupHint: '配置将自动添加到"全部"分组。您可以选择性地添加到其他分组。',
      httpWarning: '这意味着 API 等敏感信息以明文传输，请确保在安全的网络环境中使用。',
      httpWarningTitle: '安全警告',
      connectionFailed: '连接面板失败，请检查配置信息。',
      submitError: '提交配置失败，请重试。',
      addSUIConfig: '添加 S-UI 配置',
      editSUIConfig: '编辑 S-UI 配置',
      addXUIConfig: '添加 X-UI 配置',
      editXUIConfig: '编辑 X-UI 配置',
      add3XUIConfig: '添加 3X-UI 配置',
      edit3XUIConfig: '编辑 3X-UI 配置',
    },

    // 验证
    validation: {
      required: '此字段为必填项',
      invalidUrl: '请输入有效的 URL',
    },
    
    // 分组管理
    groups: {
      title: '管理分组',
      editGroups: '编辑分组',
      all: '全部',
      addGroup: '添加分组',
      rename: '重命名',
      moveUp: '上移',
      moveDown: '下移',
      deleteGroup: '删除分组',
      deleteConfirm: '确定要删除此分组吗？该分组下的所有配置将移动到全部分组。',
      groupName: '分组名称',
      enterGroupName: '请输入分组名称',
      renameGroup: '重命名分组',
      newGroupName: '新分组名称',
      cannotDeleteDefault: '无法删除全部分组',
      cannotRenameDefault: '无法重命名全部分组',
    },

    // 3X-UI 专用翻译
    threeXUI: {
      // Drawer 导航
      drawer: {
        overview: '概述',
        inbounds: '入站列表',
        routing: '路由规则',
        outbounds: '出站路由',
        backToHome: '返回主页',
      },

      // 概述页面
      overview: {
        title: '3X-UI 概述',
        loading: '加载中...',
        xrayStatus: 'Xray 状态',
        restart: '重启',
        restarting: '重启中...',
        update: '更新',
        version: '版本',
        systemResources: '系统资源',
        cpu: 'CPU',
        memory: '内存',
        disk: '磁盘',
        networkTraffic: '网络流量',
        selectXrayVersion: '选择 Xray 版本',
        install: '安装',
        installing: '安装中...',
        serverOffline: '服务器离线或连接失败',
        gettingData: '正在获取服务器数据...',
        restartSuccess: 'Xray服务重启成功',
        restartFailed: 'Xray服务重启失败',
        installSuccess: 'Xray {version} 安装成功',
        installFailed: 'Xray安装失败',
        confirmRestart: '确认重启',
        confirmRestartMessage: '确定要重启Xray服务吗？这将应用新的路由规则配置。',
        ipCopySuccess: '{version} 地址已复制到剪切板',
        ipCopyFailed: 'IP地址复制失败',
      },

      // 路由规则
      routing: {
        title: '路由规则',
        add: '添加',
        save: '保存',
        restart: '重启',
        updateGeo: '更新geo',
        updating: '更新中 {percentage}%',
        noRules: '暂无路由规则',
        noRulesSubtext: '点击上方添加按钮创建第一条路由规则',
        editRule: '编辑规则',
        deleteRule: '删除规则',
        confirmDelete: '确认删除',
        confirmDeleteMessage: '确定要删除此路由规则吗？此操作不可撤销。',
        confirmRestart: '确认重启',
        confirmRestartMessage: '确定要重启Xray服务吗？这将应用新的路由规则配置。',
        confirmUpdateGeo: '确认更新',
        confirmUpdateGeoMessage: '确定要更新geo数据库吗？这将下载最新的geosite和geoip数据。',
        updateGeoSuccess: '更新完成！',
        updateGeoFailed: '更新失败',
        saveSuccess: '路由规则已保存',
        saveFailed: '保存失败',
        loadFailed: '加载路由配置失败',
        restartSuccess: 'Xray服务重启成功',
        restartFailed: 'Xray服务重启失败',
        updateFailed: '更新失败',
        unknownError: '未知错误',
        ruleDescription: {
          domain: '域名',
          ip: 'IP',
          port: '端口',
          network: '网络',
          protocol: '协议',
          inbound: '入站',
          emptyRule: '空规则',
          rule: '规则 {number}',
        },
      },

      // 入站配置
      inbounds: {
        title: '入站列表',
        add: '添加',
        userManagement: '用户管理',
        noInbounds: '暂无入站配置',
        noInboundsSubtext: '点击上方按钮添加第一个入站配置',
        editConfig: '编辑配置',
        exportLinks: '导出链接',
        resetTraffic: '重置流量',
        exportJson: '导出JSON',
        enable: '启用',
        disable: '禁用',
        deleteConfig: '删除配置',
        confirmDelete: '确认删除',
        confirmDeleteMessage: '确定要删除此入站配置吗？此操作不可撤销。',
        confirmResetTraffic: '确认重置',
        confirmResetTrafficMessage: '确定要重置此入站配置的流量统计吗？',
        configNotFound: '配置不存在',
        loadFailed: '加载入站列表失败',
        enableSuccess: '已启用配置',
        disableSuccess: '已禁用配置',
        operationFailed: '操作失败',
        resetSuccess: '流量已重置',
        resetFailed: '重置失败',
        exportSuccess: '配置JSON已复制到剪贴板',
        exportFailed: '导出失败',
        deleteSuccess: '配置已删除',
        deleteFailed: '删除失败',
        userManagementComingSoon: '用户管理功能即将推出',
        status: {
          valid: '有效',
          invalid: '无效',
        },
      },

      // 路由规则配置
      ruleConfig: {
        title: '添加路由规则',
        editTitle: '编辑路由规则',
        domainMatcher: '域名匹配器',
        domain: '域名',
        domainPlaceholder: '输入域名，用逗号分隔',
        ip: 'IP地址',
        ipPlaceholder: '输入IP地址或CIDR，用逗号分隔',
        port: '端口',
        portPlaceholder: '例如: 53,443,1000-2000',
        sourcePort: '源端口',
        sourcePortPlaceholder: '例如: 53,443,1000-2000',
        network: '网络类型',
        source: '源地址',
        sourcePlaceholder: '输入源IP地址，用逗号分隔',
        user: '用户',
        userPlaceholder: '输入用户邮箱，用逗号分隔',
        inboundTag: '入站标签',
        protocol: '协议',
        httpAttrs: 'HTTP 属性',
        attrKeyPlaceholder: ':method',
        attrValuePlaceholder: 'GET',
        outboundTag: '出站标签',
        balancerTag: '负载均衡标签',
        balancerTagPlaceholder: '输入负载均衡标签',
        saveRule: '保存规则',
        configNotFound: '未找到配置信息',
        saveSuccess: '路由规则已添加',
        updateSuccess: '路由规则已更新',
        saveFailed: '保存失败',
      },

      // 导出链接
      exportLinks: {
        title: '导出链接',
        noLinks: '没有可用的用户链接',
        close: '关闭',
        copySuccess: '{label} 的链接已复制到剪贴板',
        copyFailed: '复制失败',
        generateFailed: '生成链接失败',
        user: '用户 {number}',
      },

      // 出站配置
      outbounds: {
        title: '出站列表',
        add: '添加',
        restart: '重启',
        restarting: '重启中...',
        noOutbounds: '暂无出站配置',
        noOutboundsSubtext: '点击右上角添加按钮创建新的出站配置',
        editConfig: '编辑配置',
        deleteConfig: '删除配置',
        confirmDelete: '确认删除',
        confirmDeleteMessage: '确定要删除此出站配置吗？此操作不可撤销。',
        confirmRestart: '确认重启',
        confirmRestartMessage: '确定要重启Xray服务吗？这将应用新的出站配置。',
        loadFailed: '加载出站列表失败',
        deleteSuccess: '出站配置已删除',
        deleteFailed: '删除失败',
        restartSuccess: 'Xray服务重启成功',
        restartFailed: 'Xray服务重启失败',
        outboundName: '出站-{number}',

        // WARP配置
        warp: {
          title: 'WARP配置',
          addWarp: '添加WARP',
          addWarpMessage: '还没有获取WARP配置，是否现在添加？',
          addOutbound: '添加出站',
          delete: '删除',
          confirmDeleteWarp: '确认删除',
          confirmDeleteWarpMessage: '确定要删除WARP配置吗？此操作不可撤销。',
          deleteWarpSuccess: 'WARP配置已删除',
          deleteWarpFailed: '删除WARP配置失败',
          getWarpFailed: '获取WARP配置失败',
          warpExists: '已存在WARP出站配置',
          name: '名称:',
          type: '类型:',
          accountType: '账户类型:',
          ipv4Address: 'IPv4地址:',
          ipv6Address: 'IPv6地址:',
          endpoint: '端点:',
          expiryTime: '到期时间:',
          unknown: '未知',
        },
      },

      // 入站配置表单
      inboundConfig: {
        addTitle: '添加入站配置',
        editTitle: '编辑入站配置',
        save: '保存配置',
        update: '更新配置',
        importFromClipboard: '从剪切板导入',
        importSuccess: '配置已从剪切板导入',
        importError: '导入失败',
        importErrorEmpty: '剪切板为空',
        importErrorInvalid: '剪切板内容不是有效的入站配置',
        importErrorFormat: '剪切板内容不是有效的JSON格式',

        // 基本设置
        protocol: '协议',
        protocolPlaceholder: '选择协议',
        listen: '监听',
        listenPlaceholder: '留空则为0.0.0.0',
        port: '端口',
        portPlaceholder: '必填，1080',
        expiryTime: '有效期（天）',
        expiryTimePlaceholder: '留空则永久有效',
        totalTraffic: '总流量（GB）',
        totalTrafficPlaceholder: '留空则不限流量',

        // 协议选项
        protocols: {
          vmess: 'VMess',
          vless: 'VLESS',
          trojan: 'Trojan',
          shadowsocks: 'Shadowsocks',
          dokodemo: 'Dokodemo-door',
          socks: 'SOCKS',
          http: 'HTTP',
          wireguard: 'WireGuard',
        },

        // 用户管理
        userManagement: '用户管理',
        addUser: '添加用户',
        noUsers: '暂无用户，点击上方按钮添加用户',
        email: '邮箱',
        emailPlaceholder: '请输入邮箱',
        uuid: 'UUID',
        uuidPlaceholder: '请输入UUID',
        password: '密码',
        passwordPlaceholder: '请输入密码',
        enableXtlsFlow: '启用 XTLS Flow',
        flow: 'Flow',

        // 回落管理
        fallbackManagement: '回落管理',
        addFallback: '添加回落',
        noFallbacks: '暂无回落配置，点击上方按钮添加回落',
        fallbackName: '名称',
        fallbackNamePlaceholder: '请输入回落名称',
        fallbackDefaultName: '回落',
        fallbackAlpn: 'ALPN',
        fallbackAlpnPlaceholder: '请输入ALPN',
        fallbackPath: '路径',
        fallbackPathPlaceholder: '请输入路径',
        fallbackDest: '目标',
        fallbackDestPlaceholder: '请输入目标端口',
        fallbackXver: 'Xver',
        fallbackXverPlaceholder: '请输入xver值',

        // Shadowsocks 设置
        shadowsocksSettings: 'Shadowsocks 设置',
        encryptionMethod: '加密方法',
        encryptionMethodPlaceholder: '选择加密方法',
        networkType: '网络类型',
        networkTypePlaceholder: '选择网络类型',

        // HTTP 设置
        httpSettings: 'HTTP 设置',
        useAuth: '使用认证',
        username: '用户名',
        usernamePlaceholder: '用户名',
        allowTransparent: '允许透明代理',

        // SOCKS 设置
        socksSettings: 'SOCKS 设置',
        enableUdp: '启用 UDP',
        ipAddress: 'IP 地址',

        // Dokodemo-door 设置
        dokodemoSettings: 'Dokodemo-door 设置',
        targetAddress: '目标地址',
        targetAddressPlaceholder: '*******',
        targetPort: '目标端口',
        targetPortPlaceholder: '53',
        followRedirect: '跟随重定向',

        // WireGuard 设置
        wireguardSettings: 'WireGuard 设置',
        privateKey: '私钥',
        privateKeyPlaceholder: '私钥',
        publicKey: '公钥',
        publicKeyPlaceholder: '公钥',
        mtu: 'MTU',
        mtuPlaceholder: '1420',
        peerManagement: 'Peer 管理',
        addPeer: '添加 Peer',
        noPeers: '暂无 Peer，点击上方按钮添加 Peer',
        allowedIPs: '允许的IP',
        allowedIPsPlaceholder: '0.0.0.0/0, ::/0',

        // 传输设置
        transportSettings: '传输设置',
        transportType: '传输方式',
        edit: '编辑',
        tcpHeaderType: '伪装类型',
        tcpHeaderTypePlaceholder: '选择TCP头部类型',

        // 安全设置
        securitySettings: '安全设置',
        securityType: '安全类型',
        securityTypePlaceholder: '选择安全类型',

        // SockOpt 设置
        sockOptSettings: 'SockOpt 设置',
        sockOptEnabled: 'SockOpt: 已启用',
        sockOptDisabled: 'SockOpt 已禁用',

        // Sniffing 设置
        sniffingSettings: 'Sniffing 设置',
        sniffingEnabled: 'Sniffing: 已启用',
        sniffingDisabled: 'Sniffing 已禁用',
        destOverride: '目标覆盖',
        metadataOnly: '仅元数据',
        routeOnly: '仅路由',
        excludedDomains: '排除域名',

        // 错误消息
        errorProtocolRequired: '请选择协议',
        errorPortRequired: '请输入端口',
        errorPortInvalid: '端口必须是1-65535之间的数字',
        errorEmailRequired: '请输入邮箱',
        errorUuidRequired: '请输入UUID',
        errorPasswordRequired: '请输入密码',
        errorPublicKeyRequired: '请输入公钥',
        errorConfigNotFound: '未找到配置',
        errorServerConfigNotFound: '未找到服务器配置',
        errorSaveFailed: '保存配置失败，请检查网络连接',
        errorGenerateKeysFailed: '生成密钥失败',
        errorCertPathRequired: '请输入证书文件路径和密钥文件路径',
        errorCertContentRequired: '请输入证书内容和密钥内容',

        // 成功消息
        successConfigAdded: '配置已添加',
        successConfigUpdated: '配置已更新',

        // 确认对话框
        confirmDelete: '确认删除',
        confirmDeleteUser: '确定要删除用户 {email} 吗？',
        confirmDeleteFallback: '确定要删除这个回落配置吗？',
        confirmDeletePeer: '确定要删除这个Peer吗？',
        confirmDeleteCert: '确定要删除这个证书吗？',

        // TLS 设置
        tlsSettings: 'TLS 设置',
        rejectUnknownSni: '拒绝未知SNI',
        allowInsecure: '允许不安全连接',
        disableSystemRoot: '禁用系统根证书',
        enableSessionResumption: '启用会话恢复',
        alpn: 'ALPN',
        minVersion: '最小版本',
        maxVersion: '最大版本',
        certificates: '证书',
        addCertificate: '添加证书',
        noCertificates: '暂无证书配置',

        // Reality 设置
        realitySettings: 'Reality 设置',
        target: '目标',
        targetPlaceholder: 'example.com:443',
        serverNames: '服务器名称',
        serverNamesPlaceholder: 'example.com,www.example.com',
        shortIds: 'Short IDs',
        shortIdsPlaceholder: '短ID列表，用逗号分隔',
        maxTimeDiff: '最大时间差',

        // 传输协议设置
        acceptProxyProtocol: '接受代理协议',
        httpMasquerading: 'HTTP 伪装',
        httpMasqueradingSettings: 'HTTP 伪装设置',
        masqueradingType: '伪装类型',
        masqueradingTypePlaceholder: '选择伪装类型',

        // TCP 设置
        tcpSettings: 'TCP 设置',

        // XHTTP 设置
        xhttpSettings: 'XHTTP 设置',
        host: '主机',
        hostPlaceholder: 'xray.com',
        path: '路径',
        pathPlaceholder: '/path',
        mode: '模式',

        // mKCP 设置
        mkcpSettings: 'mKCP 设置',
        mkcpMtu: 'MTU',
        mkcpMtuPlaceholder: '1350',
        tti: 'TTI',
        ttiPlaceholder: '20',
        uplinkCapacity: '上行容量',
        uplinkCapacityPlaceholder: '5',
        downlinkCapacity: '下行容量',
        downlinkCapacityPlaceholder: '20',
        congestion: '拥塞控制',
        readBufferSize: '读取缓冲区大小',
        writeBufferSize: '写入缓冲区大小',

        // gRPC 设置
        grpcSettings: 'gRPC 设置',
        authority: 'Authority',
        authorityPlaceholder: 'grpc.example.com',
        serviceName: '服务名',
        serviceNamePlaceholder: 'name',
        initialWindowsSize: '初始窗口大小',
        initialWindowsSizePlaceholder: '0',

        // WebSocket 设置
        websocketSettings: 'WebSocket 设置',
        heartbeatPeriod: '心跳周期',
        heartbeatPeriodPlaceholder: '10',





        // 提示信息
        realityProtocolNote: '注意：只有 VLESS 和 Trojan 协议支持 Reality',
        tcpFastOpenEnabled: 'TCP Fast Open: 已启用',

        // 按钮文本
        cancel: '取消',
        delete: '删除',
        generate: '生成',
        yes: '是',
        no: '否',
      },

      // 出站配置表单
      outboundConfig: {
        addTitle: '添加出站配置',
        editTitle: '编辑出站配置',
        save: '保存配置',
        importFromClipboard: '从剪切板导入',
        importSuccess: '协议链接已导入到配置表单',
        importError: '导入失败',
        importErrorEmpty: '剪切板为空',
        importErrorUnsupported: '剪切板内容不是支持的协议链接',
        parseError: '解析Wireguard链接失败',

        // 基本设置
        basicSettings: '基本设置',
        tag: '标签',
        tagPlaceholder: '请输入出站标签',
        protocol: '协议',
        protocolPlaceholder: '选择协议',
        sendThrough: '发送地址',
        sendThroughPlaceholder: '本地IP地址（可选）',

        // 协议选项
        protocols: {
          freedom: 'Freedom',
          blackhole: 'Blackhole',
          dns: 'DNS',
          http: 'HTTP',
          socks: 'SOCKS',
          shadowsocks: 'Shadowsocks',
          vless: 'VLESS',
          vmess: 'VMess',
          trojan: 'Trojan',
          wireguard: 'Wireguard',
        },

        // Freedom 设置
        freedomSettings: 'Freedom 设置',
        domainStrategy: '域名策略',
        domainStrategyPlaceholder: '选择域名策略',
        proxyProtocolVersion: '代理协议版本',
        redirectAddress: '重定向地址',
        redirectAddressPlaceholder: '127.0.0.1:3366',

        // Fragment 设置
        fragmentSettings: 'Fragment 设置',
        fragmentEnabled: 'Fragment: 已启用',
        fragmentDisabled: 'Fragment 已禁用',
        fragmentPackets: '数据包类型',
        fragmentLength: '长度范围',
        fragmentInterval: '间隔',

        // Noises 设置
        noisesSettings: 'Noises 设置',
        addNoise: '添加 Noise',
        noNoises: '暂无 Noise 配置，点击上方按钮添加 Noise',
        noiseType: '类型',
        noiseDelay: '延迟',
        noisePacket: '数据包',

        // Blackhole 设置
        blackholeSettings: 'Blackhole 设置',
        responseType: '响应类型',
        responseNone: '无响应',
        responseHttp: 'HTTP 响应',

        // DNS 设置
        dnsSettings: 'DNS 设置',
        networkType: '网络类型',
        nonIPQuery: '非IP查询处理',
        dnsServer: 'DNS 服务器地址',
        dnsServerPlaceholder: '*******',
        port: '端口',
        portPlaceholder: '53',
        drop: '丢弃',
        skip: '跳过',

        // HTTP 设置
        httpSettings: 'HTTP 设置',
        serverAddress: '服务器地址',
        serverAddressPlaceholder: 'proxy.example.com',
        serverPort: '服务器端口',
        serverPortPlaceholder: '3128',
        username: '用户名',
        usernamePlaceholder: '请输入用户名',
        password: '密码',
        passwordPlaceholder: '请输入密码',

        // SOCKS 设置
        socksSettings: 'SOCKS 设置',
        socksPortPlaceholder: '1080',

        // Shadowsocks 设置
        shadowsocksSettings: 'Shadowsocks 设置',
        method: '加密方式',
        methodPlaceholder: '选择加密方式',
        ssPortPlaceholder: '8388',
        uot: 'UDP over TCP',
        uotVersion: 'UoT 版本',

        // VLESS 设置
        vlessSettings: 'VLESS 设置',
        userId: '用户ID',
        userIdPlaceholder: '请输入用户ID',
        flow: '流控',
        flowPlaceholder: 'xtls-rprx-vision（可选）',
        vlessPortPlaceholder: '443',

        // VMess 设置
        vmessSettings: 'VMess 设置',
        security: '安全性',
        securityPlaceholder: '选择安全方式',
        vmessPortPlaceholder: '443',

        // Trojan 设置
        trojanSettings: 'Trojan 设置',
        trojanPortPlaceholder: '443',

        // Wireguard 设置
        wireguardSettings: 'Wireguard 设置',
        secretKey: '私钥',
        secretKeyPlaceholder: '请输入私钥',
        endpoint: '端点',
        endpointPlaceholder: 'server.example.com:51820',
        publicKey: '公钥',
        publicKeyPlaceholder: '请输入公钥',
        mtu: 'MTU',
        mtuPlaceholder: '1420',
        address: '地址',
        addressPlaceholder: '********/32, fd00::2/128',
        noKernelTun: '禁用内核TUN',
        reserved: '保留字段',
        reservedPlaceholder: '0,0,0',
        workers: '工作线程',
        workersPlaceholder: '工作线程数量',
        wgDomainStrategy: '域名策略',

        // 传输设置
        transportSettings: '传输设置',
        transportType: '传输方式',
        transportEnabled: '传输方式: 已启用',
        transportDisabled: '传输设置已禁用',
        edit: '编辑',

        // TCP 设置
        tcpSettings: 'TCP 设置',
        headerType: '伪装类型',
        httpVersion: 'HTTP 版本',
        httpMethod: 'HTTP 方法',
        httpPaths: 'HTTP 路径',
        httpHeaders: 'HTTP 头部',
        responseVersion: '响应版本',
        responseStatus: '响应状态',
        responseReason: '响应原因',

        // XHTTP 设置
        xhttpSettings: 'XHTTP 设置',
        host: '主机',
        hostPlaceholder: 'example.com',
        path: '路径',
        pathPlaceholder: '/',
        mode: '模式',
        headers: '头部',
        paddingBytes: '填充字节',
        noGRPCHeader: '禁用 gRPC 头部',
        minPostsInterval: '最小发送间隔',
        maxConcurrency: '最大并发数',
        maxConnections: '最大连接数',
        maxReuseTimes: '最大重用次数',
        maxRequestTimes: '最大请求次数',
        maxReusableSecs: '最大重用秒数',
        keepAlivePeriod: '保活周期',
        downloadAddress: '下载地址',
        downloadPort: '下载端口',
        downloadNetwork: '下载网络',

        // mKCP 设置
        mkcpSettings: 'mKCP 设置',
        tti: 'TTI',
        uplinkCapacity: '上行容量',
        downlinkCapacity: '下行容量',
        congestion: '拥塞控制',
        readBufferSize: '读缓冲区大小',
        writeBufferSize: '写缓冲区大小',
        domain: '域名',
        seed: '种子',

        // gRPC 设置
        grpcSettings: 'gRPC 设置',
        authority: '权威',
        serviceName: '服务名称',
        userAgent: '用户代理',
        multiMode: '多路模式',
        idleTimeout: '空闲超时',
        healthCheckTimeout: '健康检查超时',
        permitWithoutStream: '允许无流',
        initialWindowsSize: '初始窗口大小',

        // WebSocket 设置
        websocketSettings: 'WebSocket 设置',
        heartbeatPeriod: '心跳周期',

        // HTTP Upgrade 设置
        httpUpgradeSettings: 'HTTP Upgrade 设置',

        // HTTP 伪装详细设置
        requestConfig: '请求配置',
        responseConfig: '响应配置',
        httpRequestVersion: '版本',
        httpRequestVersionPlaceholder: '1.1',
        httpRequestMethod: '方法',
        httpRequestMethodPlaceholder: 'GET',
        requestPath: '请求路径',
        requestHeaders: '请求头',
        responseHeaders: '响应头',
        httpResponseStatus: '状态',
        httpResponseStatusPlaceholder: '200',
        httpResponseReason: '原因',
        httpResponseReasonPlaceholder: 'OK',
        addHeader: '添加头部',
        headerKey: '键',
        headerValue: '值',
        headerKeyPlaceholder: 'Host',
        headerValuePlaceholder: '值',
        customHeaders: '自定义 Headers',
        noHeaders: '暂无头部配置',

        // XHTTP 额外设置
        xPaddingBytes: 'X-Padding 字节',
        xPaddingBytesPlaceholder: '100-1000',
        noSSEHeader: '禁用 SSE 头部',
        scMaxEachPostBytes: 'SC 每次 Post 最大字节',
        scMaxEachPostBytesPlaceholder: '1000000',
        scMaxConcurrentPosts: 'SC 最大并发 Posts',
        scMaxConcurrentPostsPlaceholder: '100',
        scMinPostsIntervalMs: 'SC Posts 最小间隔 (ms)',
        scMinPostsIntervalMsPlaceholder: '30',

        // 传输详情显示
        modeLabel: '模式',
        headersCount: 'Headers',
        headersCountSuffix: '个',
        hostLabel: '主机',
        pathLabel: '路径',
        notSet: '未设置',
        sockOptDomainStrategy: '域名策略',

        // 路径管理
        httpRequestPath: '请求路径',
        addPath: '添加路径',

        // 伪装设置
        masqueradingDomain: '伪装域名',
        masqueradingDomainPlaceholder: 'example.com',

        // mKCP 种子密钥
        seedKey: '种子密钥',
        seedKeyPlaceholder: 'Password',

        // Reality 密钥占位符
        realityPrivateKeyPlaceholder: '私钥',
        realityPublicKeyPlaceholder: '公钥',
        realityPublicKeyLabel: '公钥',

        // 证书密钥内容
        certKeyContentLabel: '密钥内容',
        certKeyContentPlaceholder: '-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----',

        // TLS 版本和协议设置
        alpnProtocol: 'ALPN协议',
        selectVersion: '选择版本',

        // TLS 版本选项
        tlsVersion10: '1.0',
        tlsVersion11: '1.1',
        tlsVersion12: '1.2',
        tlsVersion13: '1.3',

        // 证书相关
        certificateContent: '证书内容',
        certificateContentPlaceholder: '-----BEGIN CERTIFICATE-----\\n...\\n-----END CERTIFICATE-----',
        certificateCount: '证书数量',
        certificateCountSuffix: '',

        // 选择占位符和提示
        selectMode: '选择模式',
        selectXver: '选择Xver',
        selectTproxyMode: '选择TProxy模式',
        destOverrideLabel: '目标覆盖 (destOverride)',

        // 伪装类型选项
        masqueradingNone: '无',

        // 错误消息
        errorConfigIdNotFound: '未找到配置ID',
        errorOriginalConfigNotFound: '未找到原配置',

        // Sniffing 设置
        metadataOnlyLabel: '仅元数据',
        routeOnlyLabel: '仅路由 (routeOnly)',

        // 限制设置
        limitFallbackUpload: '限制回落上传',
        limitFallbackDownload: '限制回落下载',
        afterBytes: '字节后',
        afterBytesPlaceholder: '0',
        bytesPerSec: '每秒字节',
        bytesPerSecPlaceholder: '0',
        burstBytesPerSec: '突发每秒字节',
        burstBytesPerSecPlaceholder: '0',

        // 底部弹窗标题
        sockOptSettingsTitle: 'SockOpt设置',
        sniffingSettingsTitle: 'Sniffing设置',
        certificateSettingsTitle: '证书设置',

        // 证书管理
        editCertificateTitle: '编辑证书',
        addCertificateTitle: '添加证书',
        certAddMethod: '添加方式',
        certFileContent: '文件内容',
        certFilePath: '文件路径',
        certificateFilePath: '证书文件路径',
        keyFilePath: '密钥文件路径',

        // 域名排除
        domainsExcluded: '域名排除列表',
        domainsExcludedPlaceholder: '输入要排除的域名，用逗号分隔',

        // 选择占位符
        selectDomainStrategy: '选择域名策略',
        selectAddressPortStrategy: '选择地址端口策略',

        // 安全设置
        securitySettings: '安全设置',
        securityType: '安全类型',
        securityEnabled: '安全设置: 已启用',
        securityDisabled: '安全设置已禁用',

        // TLS 设置
        tlsSettings: 'TLS 设置',
        serverName: '服务器名称',
        serverNamePlaceholder: 'example.com',
        allowInsecure: '允许不安全连接',
        alpn: 'ALPN',
        fingerprint: '指纹',
        pinnedPeerCerts: '固定对等证书',
        pinnedPeerCertsPlaceholder: '用逗号分隔的SHA256哈希值',

        // Reality 设置
        realitySettings: 'Reality 设置',
        realityPassword: '密码',
        shortId: '短ID',
        spiderX: 'Spider X',

        // Mux 设置
        muxSettings: 'Mux 设置',
        muxEnabled: '多路复用: 已启用',
        muxDisabled: '多路复用已禁用',
        concurrency: '并发数',
        xudpConcurrency: 'XUDP并发数',
        xudpProxyUDP443: 'XUDP代理UDP443',
        reject: '拒绝',
        allow: '允许',

        // SockOpt 设置
        sockOptSettings: 'SockOpt 设置',
        sockOptEnabled: 'SockOpt: 已启用',
        sockOptDisabled: 'SockOpt已禁用',
        mark: '标记',
        tcpMaxSeg: 'TCP最大段',
        tcpFastOpen: 'TCP快速打开',
        tproxy: 'TProxy',
        dialerProxy: '拨号代理',
        dialerProxyPlaceholder: '请输入代理标签',
        tcpKeepAliveInterval: 'TCP保活间隔',
        tcpKeepAliveIdle: 'TCP保活空闲',
        tcpUserTimeout: 'TCP用户超时',
        tcpCongestion: 'TCP拥塞控制',
        interface: '接口',
        v6Only: '仅IPv6',
        tcpWindowClamp: 'TCP窗口限制',
        addressPortStrategy: '地址端口策略',

        // 安全设置新增
        realityProtocolHint: '仅 VLESS 和 Trojan 协议支持 Reality',
        realityServerNamePlaceholder: 'example.com,www.example.com',
        shortIdPlaceholder: '01234ef',

        // 其他翻译
        serverNotFound: '未找到服务器配置',
        updateSuccess: '出站配置已更新',
        addSuccess: '出站配置已添加',

        // 代理设置
        proxySettings: '代理设置',
        proxyTag: '代理标签',
        proxyTagPlaceholder: '请输入代理标签',

        // 通用
        none: '无',
        auto: '自动',
        tcp: 'TCP',
        udp: 'UDP',
        websocket: 'WebSocket',
        http2: 'HTTP/2',
        grpc: 'gRPC',
        xhttp: 'XHTTP',
        mkcp: 'mKCP',
        httpupgrade: 'HTTP Upgrade',
        tls: 'TLS',
        reality: 'Reality',
        version0: '版本 0',
        version1: '版本 1',
        version2: '版本 2',
        off: '关闭',
        redirect: '重定向',
        tun: 'TUN',
        original: '原始',

        // 错误消息
        saveError: '保存配置失败',
        saveSuccess: '配置保存成功',
        validationError: '请填写所有必填字段',
        tagRequired: '标签为必填项',
        serverAddressRequired: '服务器地址为必填项',
        serverPortRequired: '服务器端口为必填项',
        userIdRequired: '用户ID为必填项',
        passwordRequired: '密码为必填项',
        secretKeyRequired: '私钥为必填项',
        publicKeyRequired: '公钥为必填项',
        endpointRequired: '端点为必填项',
      },
    },

    // 设置
    settings: {
      title: '设置',
      theme: '主题',
      language: '语言',
      proPlan: 'Pro 计划',
      subscribeToPro: '订阅 Pro',
      messengerGroup: 'Messenger 群组',
      proActive: 'Pro 订阅已激活',
      proDescription: '升级到 Pro 版本以添加无限配置',
      configLimit: '配置限制',
      configLimitReached: '您已达到配置限制。升级到 Pro 版本以添加更多配置。',
      configLimitError: '已达到配置限制。升级到 Pro 版本以添加更多配置。',
      restorePurchases: '恢复购买',
      purchaseError: '购买失败，请重试。',
      restoreSuccess: '购买恢复成功',
      restoreError: '恢复购买失败',
      openLinkError: '打开链接失败',
      privacyPolicy: '隐私政策',
      clearData: '清除所有数据',
      clearDataConfirmMessage: '这将删除所有应用数据，包括配置、分组、设置和缓存状态。该操作不可恢复，是否继续？',
      clearDataSuccess: '数据已全部清除',
      clearDataError: '清除数据失败',
      themes: {
        light: '浅色',
        dark: '深色',
        system: '跟随系统',
      },
      languages: {
        en: 'English',
        'zh-CN': '简体中文',
        'zh-TW': '繁体中文',
        fa: 'فارسی',
      },
    },
  },
  
  'zh-TW': {
    // 通用
    common: {
      add: '新增',
      edit: '編輯',
      delete: '刪除',
      save: '儲存',
      saving: '儲存中...',
      cancel: '取消',
      ok: '確定',
      confirm: '確認',
      loading: '載入中...',
      connecting: '連接中...',
      error: '錯誤',
      success: '成功',
      name: '名稱',
      url: '網址',
      username: '使用者名稱',
      password: '密碼',
      protocol: '協定',
      certificate: '憑證',
      group: '群組',
      all: '全部',
      default: '預設',
      deleteConfirmMessage: '確定要刪除此設定嗎？',
      deleteError: '刪除設定失敗',
      import: '匯入',
      importConfig: '匯入設定',
      importConfigFrom: '從設定匯入',
      selectConfigToImport: '選擇要匯入的設定',
      importSuccess: '設定匯入成功',
      importError: '設定匯入失敗',
      unsupportedType: '不支援的設定類型',
      scanQRCode: '掃描二維碼',
      generateQRCode: '產生',
      yes: '是',
      no: '否',
      value: '值',
      unknownError: '未知錯誤',
      developing: '開發中',
    },
    
    // 导航
    navigation: {
      home: '主頁',
      settings: '設定',
      addConfig: '新增設定',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: '服務器列表',
      addConfiguration: '新增設定',
      editGroups: '編輯群組',
      noConfigurations: '暫無設定',
      addFirstConfig: '新增您的第一個設定',
    },
    
    // 添加配置
    addConfig: {
      title: '新增設定',
      selectType: '選擇設定類型',
      configName: '設定名稱',
      apiKey: 'API 金鑰',
      httpWarning: '這意味著 API 等敏感資訊以明文傳輸，請確保在安全的網路環境中使用。',
      certTooltip: '如果為自簽憑證請填寫',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: '選擇群組',
      submit: '新增設定',
    },

    // 配置表单
    configForm: {
      name: '設定名稱',
      namePlaceholder: '請輸入設定名稱',
      protocol: '協定',
      url: '網址',
      username: '使用者名稱',
      usernamePlaceholder: '請輸入使用者名稱',
      password: '密碼',
      passwordPlaceholder: '請輸入密碼',
      api: 'API 金鑰',
      apiPlaceholder: '請輸入 API 金鑰',
      cert: '憑證',
      certTooltip: '如果為自簽憑證請填寫',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      publicKeyHash: '公鑰雜湊 (SHA256)',
      publicKeyHashTooltip: '固定憑證僅用於在與系統信任憑證通信時進一步加強安全性，自簽憑證仍然會連接失敗',
      ipAddressCertWarning: 'IP位址無法用於SSL憑證固定，請使用網域名稱。',
      publicKeyHashPlaceholder: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456\n9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba',
      group: '群組',
      groupHint: '設定將自動新增到「全部」群組。您可以選擇性地新增到其他群組。',
      httpWarning: '這意味著 API 等敏感資訊以明文傳輸，請確保在安全的網路環境中使用。',
      httpWarningTitle: '安全警告',
      connectionFailed: '連接面板失敗，請檢查設定資訊。',
      submitError: '提交設定失敗，請重試。',
      addSUIConfig: '新增 S-UI 設定',
      editSUIConfig: '編輯 S-UI 設定',
      addXUIConfig: '新增 X-UI 設定',
      editXUIConfig: '編輯 X-UI 設定',
      add3XUIConfig: '新增 3X-UI 設定',
      edit3XUIConfig: '編輯 3X-UI 設定',
    },

    // 验证
    validation: {
      required: '此欄位為必填項',
      invalidUrl: '請輸入有效的 URL',
    },
    
    // 分组管理
    groups: {
      title: '管理群組',
      editGroups: '編輯群組',
      all: '全部',
      addGroup: '新增群組',
      rename: '重新命名',
      moveUp: '上移',
      moveDown: '下移',
      deleteGroup: '刪除群組',
      deleteConfirm: '確定要刪除此群組嗎？該群組下的所有設定將移動到全部群組。',
      groupName: '群組名稱',
      enterGroupName: '請輸入群組名稱',
      renameGroup: '重新命名群組',
      newGroupName: '新群組名稱',
      cannotDeleteDefault: '無法刪除全部群組',
      cannotRenameDefault: '無法重新命名全部群組',
    },

    // 3X-UI 專用翻譯
    threeXUI: {
      // Drawer 導航
      drawer: {
        overview: '概述',
        inbounds: '入站列表',
        routing: '路由規則',
        outbounds: '出站路由',
        backToHome: '返回主頁',
      },

      // 概述頁面
      overview: {
        title: '3X-UI 概述',
        loading: '載入中...',
        xrayStatus: 'Xray 狀態',
        restart: '重啟',
        restarting: '重啟中...',
        update: '更新',
        version: '版本',
        systemResources: '系統資源',
        cpu: 'CPU',
        memory: '記憶體',
        disk: '磁碟',
        networkTraffic: '網路流量',
        selectXrayVersion: '選擇 Xray 版本',
        install: '安裝',
        installing: '安裝中...',
        serverOffline: '伺服器離線或連接失敗',
        gettingData: '正在獲取伺服器資料...',
        restartSuccess: 'Xray服務重啟成功',
        restartFailed: 'Xray服務重啟失敗',
        installSuccess: 'Xray {version} 安裝成功',
        installFailed: 'Xray安裝失敗',
        confirmRestart: '確認重啟',
        confirmRestartMessage: '確定要重啟Xray服務嗎？這將應用新的路由規則設定。',
        ipCopySuccess: '{version} 地址已複製到剪貼簿',
        ipCopyFailed: 'IP地址複製失敗',
      },

      // 路由規則
      routing: {
        title: '路由規則',
        add: '新增',
        save: '儲存',
        restart: '重啟',
        updateGeo: '更新geo',
        updating: '更新中 {percentage}%',
        noRules: '暫無路由規則',
        noRulesSubtext: '點擊上方新增按鈕建立第一條路由規則',
        editRule: '編輯規則',
        deleteRule: '刪除規則',
        confirmDelete: '確認刪除',
        confirmDeleteMessage: '確定要刪除此路由規則嗎？此操作不可撤銷。',
        confirmRestart: '確認重啟',
        confirmRestartMessage: '確定要重啟Xray服務嗎？這將應用新的路由規則設定。',
        confirmUpdateGeo: '確認更新',
        confirmUpdateGeoMessage: '確定要更新geo資料庫嗎？這將下載最新的geosite和geoip資料。',
        updateGeoSuccess: '更新完成！',
        updateGeoFailed: '更新失敗',
        saveSuccess: '路由規則已儲存',
        saveFailed: '儲存失敗',
        loadFailed: '載入路由設定失敗',
        restartSuccess: 'Xray服務重啟成功',
        restartFailed: 'Xray服務重啟失敗',
        updateFailed: '更新失敗',
        unknownError: '未知錯誤',
        ruleDescription: {
          domain: '網域',
          ip: 'IP',
          port: '連接埠',
          network: '網路',
          protocol: '協定',
          inbound: '入站',
          emptyRule: '空規則',
          rule: '規則 {number}',
        },
      },

      // 入站設定
      inbounds: {
        title: '入站列表',
        add: '新增',
        userManagement: '使用者管理',
        noInbounds: '暫無入站設定',
        noInboundsSubtext: '點擊上方按鈕新增第一個入站設定',
        editConfig: '編輯設定',
        exportLinks: '匯出連結',
        resetTraffic: '重置流量',
        exportJson: '匯出JSON',
        enable: '啟用',
        disable: '停用',
        deleteConfig: '刪除設定',
        confirmDelete: '確認刪除',
        confirmDeleteMessage: '確定要刪除此入站設定嗎？此操作不可撤銷。',
        confirmResetTraffic: '確認重置',
        confirmResetTrafficMessage: '確定要重置此入站設定的流量統計嗎？',
        configNotFound: '設定不存在',
        loadFailed: '載入入站列表失敗',
        enableSuccess: '已啟用設定',
        disableSuccess: '已停用設定',
        operationFailed: '操作失敗',
        resetSuccess: '流量已重置',
        resetFailed: '重置失敗',
        exportSuccess: '設定JSON已複製到剪貼簿',
        exportFailed: '匯出失敗',
        deleteSuccess: '設定已刪除',
        deleteFailed: '刪除失敗',
        userManagementComingSoon: '使用者管理功能即將推出',
        status: {
          valid: '有效',
          invalid: '無效',
        },
      },

      // 路由規則設定
      ruleConfig: {
        title: '新增路由規則',
        editTitle: '編輯路由規則',
        domainMatcher: '網域匹配器',
        domain: '網域',
        domainPlaceholder: '輸入網域，用逗號分隔',
        ip: 'IP位址',
        ipPlaceholder: '輸入IP位址或CIDR，用逗號分隔',
        port: '連接埠',
        portPlaceholder: '例如: 53,443,1000-2000',
        sourcePort: '來源連接埠',
        sourcePortPlaceholder: '例如: 53,443,1000-2000',
        network: '網路類型',
        source: '來源位址',
        sourcePlaceholder: '輸入來源IP位址，用逗號分隔',
        user: '使用者',
        userPlaceholder: '輸入使用者信箱，用逗號分隔',
        inboundTag: '入站標籤',
        protocol: '協定',
        httpAttrs: 'HTTP 屬性',
        attrKeyPlaceholder: ':method',
        attrValuePlaceholder: 'GET',
        outboundTag: '出站標籤',
        balancerTag: '負載平衡標籤',
        balancerTagPlaceholder: '輸入負載平衡標籤',
        saveRule: '儲存規則',
        configNotFound: '未找到設定資訊',
        saveSuccess: '路由規則已新增',
        updateSuccess: '路由規則已更新',
        saveFailed: '儲存失敗',
      },

      // 匯出連結
      exportLinks: {
        title: '匯出連結',
        noLinks: '沒有可用的使用者連結',
        close: '關閉',
        copySuccess: '{label} 的連結已複製到剪貼簿',
        copyFailed: '複製失敗',
        generateFailed: '產生連結失敗',
        user: '使用者 {number}',
      },

      // 出站設定
      outbounds: {
        title: '出站列表',
        add: '新增',
        restart: '重啟',
        restarting: '重啟中...',
        noOutbounds: '暫無出站設定',
        noOutboundsSubtext: '點擊右上角新增按鈕建立新的出站設定',
        editConfig: '編輯設定',
        deleteConfig: '刪除設定',
        confirmDelete: '確認刪除',
        confirmDeleteMessage: '確定要刪除此出站設定嗎？此操作不可撤銷。',
        confirmRestart: '確認重啟',
        confirmRestartMessage: '確定要重啟Xray服務嗎？這將應用新的出站設定。',
        loadFailed: '載入出站列表失敗',
        deleteSuccess: '出站設定已刪除',
        deleteFailed: '刪除失敗',
        restartSuccess: 'Xray服務重啟成功',
        restartFailed: 'Xray服務重啟失敗',
        outboundName: '出站-{number}',

        // WARP設定
        warp: {
          title: 'WARP設定',
          addWarp: '新增WARP',
          addWarpMessage: '還沒有獲取WARP設定，是否現在新增？',
          addOutbound: '新增出站',
          delete: '刪除',
          confirmDeleteWarp: '確認刪除',
          confirmDeleteWarpMessage: '確定要刪除WARP設定嗎？此操作不可撤銷。',
          deleteWarpSuccess: 'WARP設定已刪除',
          deleteWarpFailed: '刪除WARP設定失敗',
          getWarpFailed: '獲取WARP設定失敗',
          warpExists: '已存在WARP出站設定',
          name: '名稱:',
          type: '類型:',
          accountType: '帳戶類型:',
          ipv4Address: 'IPv4位址:',
          ipv6Address: 'IPv6位址:',
          endpoint: '端點:',
          expiryTime: '到期時間:',
          unknown: '未知',
        },
      },

      // 入站設定表單
      inboundConfig: {
        addTitle: '新增入站設定',
        editTitle: '編輯入站設定',
        save: '儲存設定',
        update: '更新設定',
        importFromClipboard: '從剪貼簿匯入',
        importSuccess: '設定已從剪貼簿匯入',
        importError: '匯入失敗',
        importErrorEmpty: '剪貼簿為空',
        importErrorInvalid: '剪貼簿內容不是有效的入站設定',
        importErrorFormat: '剪貼簿內容不是有效的JSON格式',

        // 基本設定
        protocol: '協定',
        protocolPlaceholder: '選擇協定',
        listen: '監聽',
        listenPlaceholder: '留空則為0.0.0.0',
        port: '連接埠',
        portPlaceholder: '必填，1080',
        expiryTime: '有效期（天）',
        expiryTimePlaceholder: '留空則永久有效',
        totalTraffic: '總流量（GB）',
        totalTrafficPlaceholder: '留空則不限流量',

        // 協定選項
        protocols: {
          vmess: 'VMess',
          vless: 'VLESS',
          trojan: 'Trojan',
          shadowsocks: 'Shadowsocks',
          dokodemo: 'Dokodemo-door',
          socks: 'SOCKS',
          http: 'HTTP',
          wireguard: 'WireGuard',
        },

        // 使用者管理
        userManagement: '使用者管理',
        addUser: '新增使用者',
        noUsers: '暫無使用者，點擊上方按鈕新增使用者',
        email: '電子郵件',
        emailPlaceholder: '請輸入電子郵件',
        uuid: 'UUID',
        uuidPlaceholder: '請輸入UUID',
        password: '密碼',
        passwordPlaceholder: '請輸入密碼',
        enableXtlsFlow: '啟用 XTLS Flow',
        flow: 'Flow',

        // 回落管理
        fallbackManagement: '回落管理',
        addFallback: '新增回落',
        noFallbacks: '暫無回落設定，點擊上方按鈕新增回落',
        fallbackName: '名稱',
        fallbackNamePlaceholder: '請輸入回落名稱',
        fallbackDefaultName: '回落',
        fallbackAlpn: 'ALPN',
        fallbackAlpnPlaceholder: '請輸入ALPN',
        fallbackPath: '路徑',
        fallbackPathPlaceholder: '請輸入路徑',
        fallbackDest: '目標',
        fallbackDestPlaceholder: '請輸入目標連接埠',
        fallbackXver: 'Xver',
        fallbackXverPlaceholder: '請輸入xver值',

        // Shadowsocks 設定
        shadowsocksSettings: 'Shadowsocks 設定',
        encryptionMethod: '加密方法',
        encryptionMethodPlaceholder: '選擇加密方法',
        networkType: '網路類型',
        networkTypePlaceholder: '選擇網路類型',

        // HTTP 設定
        httpSettings: 'HTTP 設定',
        useAuth: '使用認證',
        username: '使用者名稱',
        usernamePlaceholder: '使用者名稱',
        allowTransparent: '允許透明代理',

        // SOCKS 設定
        socksSettings: 'SOCKS 設定',
        enableUdp: '啟用 UDP',
        ipAddress: 'IP 位址',

        // Dokodemo-door 設定
        dokodemoSettings: 'Dokodemo-door 設定',
        targetAddress: '目標位址',
        targetAddressPlaceholder: '*******',
        targetPort: '目標連接埠',
        targetPortPlaceholder: '53',
        followRedirect: '跟隨重新導向',

        // WireGuard 設定
        wireguardSettings: 'WireGuard 設定',
        privateKey: '私鑰',
        privateKeyPlaceholder: '私鑰',
        publicKey: '公鑰',
        publicKeyPlaceholder: '公鑰',
        mtu: 'MTU',
        mtuPlaceholder: '1420',
        peerManagement: 'Peer 管理',
        addPeer: '新增 Peer',
        noPeers: '暫無 Peer，點擊上方按鈕新增 Peer',
        allowedIPs: '允許的IP',
        allowedIPsPlaceholder: '0.0.0.0/0, ::/0',

        // 傳輸設定
        transportSettings: '傳輸設定',
        transportType: '傳輸方式',
        edit: '編輯',
        tcpHeaderType: '偽裝類型',
        tcpHeaderTypePlaceholder: '選擇TCP標頭類型',

        // 安全設定
        securitySettings: '安全設定',
        securityType: '安全類型',
        securityTypePlaceholder: '選擇安全類型',

        // SockOpt 設定
        sockOptSettings: 'SockOpt 設定',
        sockOptEnabled: 'SockOpt: 已啟用',
        sockOptDisabled: 'SockOpt 已停用',

        // Sniffing 設定
        sniffingSettings: 'Sniffing 設定',
        sniffingEnabled: 'Sniffing: 已啟用',
        sniffingDisabled: 'Sniffing 已停用',
        destOverride: '目標覆蓋',
        metadataOnly: '僅中繼資料',
        routeOnly: '僅路由',
        excludedDomains: '排除網域',

        // 錯誤訊息
        errorProtocolRequired: '請選擇協定',
        errorPortRequired: '請輸入連接埠',
        errorPortInvalid: '連接埠必須是1-65535之間的數字',
        errorEmailRequired: '請輸入電子郵件',
        errorUuidRequired: '請輸入UUID',
        errorPasswordRequired: '請輸入密碼',
        errorPublicKeyRequired: '請輸入公鑰',
        errorConfigNotFound: '未找到設定',
        errorServerConfigNotFound: '未找到伺服器設定',
        errorSaveFailed: '儲存設定失敗，請檢查網路連接',
        errorGenerateKeysFailed: '產生金鑰失敗',
        errorCertPathRequired: '請輸入憑證檔案路徑和金鑰檔案路徑',
        errorCertContentRequired: '請輸入憑證內容和金鑰內容',

        // 成功訊息
        successConfigAdded: '設定已新增',
        successConfigUpdated: '設定已更新',

        // 確認對話框
        confirmDelete: '確認刪除',
        confirmDeleteUser: '確定要刪除使用者 {email} 嗎？',
        confirmDeleteFallback: '確定要刪除這個回落設定嗎？',
        confirmDeletePeer: '確定要刪除這個Peer嗎？',
        confirmDeleteCert: '確定要刪除這個憑證嗎？',

        // TLS 設定
        tlsSettings: 'TLS 設定',
        rejectUnknownSni: '拒絕未知SNI',
        allowInsecure: '允許不安全連接',
        disableSystemRoot: '停用系統根憑證',
        enableSessionResumption: '啟用工作階段恢復',
        alpn: 'ALPN',
        minVersion: '最小版本',
        maxVersion: '最大版本',
        certificates: '憑證',
        addCertificate: '新增憑證',
        noCertificates: '暫無憑證設定',

        // Reality 設定
        realitySettings: 'Reality 設定',
        target: '目標',
        targetPlaceholder: 'example.com:443',
        serverNames: '伺服器名稱',
        serverNamesPlaceholder: 'example.com,www.example.com',
        shortIds: 'Short IDs',
        shortIdsPlaceholder: '短ID列表，用逗號分隔',
        maxTimeDiff: '最大時間差',

        // 傳輸協定設定
        acceptProxyProtocol: '接受代理協定',
        httpMasquerading: 'HTTP 偽裝',
        httpMasqueradingSettings: 'HTTP 偽裝設定',
        masqueradingType: '偽裝類型',
        masqueradingTypePlaceholder: '選擇偽裝類型',

        // TCP 設定
        tcpSettings: 'TCP 設定',

        // XHTTP 設定
        xhttpSettings: 'XHTTP 設定',
        host: '主機',
        hostPlaceholder: 'xray.com',
        path: '路徑',
        pathPlaceholder: '/path',
        mode: '模式',

        // mKCP 設定
        mkcpSettings: 'mKCP 設定',
        mkcpMtu: 'MTU',
        mkcpMtuPlaceholder: '1350',
        tti: 'TTI',
        ttiPlaceholder: '20',
        uplinkCapacity: '上行容量',
        uplinkCapacityPlaceholder: '5',
        downlinkCapacity: '下行容量',
        downlinkCapacityPlaceholder: '20',
        congestion: '壅塞控制',
        readBufferSize: '讀取緩衝區大小',
        writeBufferSize: '寫入緩衝區大小',

        // gRPC 設定
        grpcSettings: 'gRPC 設定',
        authority: 'Authority',
        authorityPlaceholder: 'grpc.example.com',
        serviceName: '服務名稱',
        serviceNamePlaceholder: 'name',
        initialWindowsSize: '初始視窗大小',
        initialWindowsSizePlaceholder: '0',

        // WebSocket 設定
        websocketSettings: 'WebSocket 設定',
        heartbeatPeriod: '心跳週期',
        heartbeatPeriodPlaceholder: '10',

        // HTTP Upgrade 設定
        httpUpgradeSettings: 'HTTP Upgrade 設定',

        // HTTP 偽裝詳細設定
        requestConfig: '請求設定',
        responseConfig: '回應設定',
        httpRequestVersion: '版本',
        httpRequestVersionPlaceholder: '1.1',
        httpRequestMethod: '方法',
        httpRequestMethodPlaceholder: 'GET',
        requestPath: '請求路徑',
        requestHeaders: '請求標頭',
        responseHeaders: '回應標頭',
        httpResponseStatus: '狀態',
        httpResponseStatusPlaceholder: '200',
        httpResponseReason: '原因',
        httpResponseReasonPlaceholder: 'OK',
        addHeader: '新增標頭',
        headerKey: '鍵',
        headerValue: '值',
        headerKeyPlaceholder: 'Host',
        headerValuePlaceholder: '值',
        customHeaders: '自訂 Headers',
        noHeaders: '暫無標頭設定',

        // XHTTP 額外設定
        xPaddingBytes: 'X-Padding 位元組',
        xPaddingBytesPlaceholder: '100-1000',
        noSSEHeader: '停用 SSE 標頭',
        scMaxEachPostBytes: 'SC 每次 Post 最大位元組',
        scMaxEachPostBytesPlaceholder: '1000000',
        scMaxConcurrentPosts: 'SC 最大並行 Posts',
        scMaxConcurrentPostsPlaceholder: '100',
        scMinPostsIntervalMs: 'SC Posts 最小間隔 (ms)',
        scMinPostsIntervalMsPlaceholder: '30',

        // 傳輸詳情顯示
        modeLabel: '模式',
        headersCount: 'Headers',
        headersCountSuffix: '個',
        hostLabel: '主機',
        pathLabel: '路徑',
        notSet: '未設定',
        sockOptDomainStrategy: '網域策略',

        // 路徑管理
        httpRequestPath: '請求路徑',
        addPath: '新增路徑',

        // 偽裝設定
        masqueradingDomain: '偽裝網域',
        masqueradingDomainPlaceholder: 'example.com',

        // mKCP 種子金鑰
        seedKey: '種子金鑰',
        seedKeyPlaceholder: 'Password',

        // Reality 金鑰佔位符
        realityPrivateKeyPlaceholder: '私鑰',
        realityPublicKeyPlaceholder: '公鑰',
        realityPublicKeyLabel: '公鑰',

        // 憑證金鑰內容
        certKeyContentLabel: '金鑰內容',
        certKeyContentPlaceholder: '-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----',

        // TLS 版本和協定設定
        alpnProtocol: 'ALPN協定',
        selectVersion: '選擇版本',

        // TLS 版本選項
        tlsVersion10: '1.0',
        tlsVersion11: '1.1',
        tlsVersion12: '1.2',
        tlsVersion13: '1.3',

        // 憑證相關
        certificateContent: '憑證內容',
        certificateContentPlaceholder: '-----BEGIN CERTIFICATE-----\\n...\\n-----END CERTIFICATE-----',
        certificateCount: '憑證數量',
        certificateCountSuffix: '',

        // 選擇佔位符和提示
        selectMode: '選擇模式',
        selectXver: '選擇Xver',
        selectTproxyMode: '選擇TProxy模式',
        destOverrideLabel: '目標覆蓋 (destOverride)',

        // 偽裝類型選項
        masqueradingNone: '無',

        // 錯誤訊息
        errorConfigIdNotFound: '未找到設定ID',
        errorOriginalConfigNotFound: '未找到原設定',

        // Sniffing 設定
        metadataOnlyLabel: '僅中繼資料',
        routeOnlyLabel: '僅路由 (routeOnly)',

        // 限制設定
        limitFallbackUpload: '限制回落上傳',
        limitFallbackDownload: '限制回落下載',
        afterBytes: '位元組後',
        afterBytesPlaceholder: '0',
        bytesPerSec: '每秒位元組',
        bytesPerSecPlaceholder: '0',
        burstBytesPerSec: '突發每秒位元組',
        burstBytesPerSecPlaceholder: '0',

        // 底部彈窗標題
        sockOptSettingsTitle: 'SockOpt設定',
        sniffingSettingsTitle: 'Sniffing設定',
        certificateSettingsTitle: '憑證設定',

        // 憑證管理
        editCertificateTitle: '編輯憑證',
        addCertificateTitle: '新增憑證',
        certAddMethod: '新增方式',
        certFileContent: '檔案內容',
        certFilePath: '檔案路徑',
        certificateFilePath: '憑證檔案路徑',
        keyFilePath: '金鑰檔案路徑',

        // 網域排除
        domainsExcluded: '網域排除清單',
        domainsExcludedPlaceholder: '輸入要排除的網域，用逗號分隔',

        // 選擇佔位符
        selectDomainStrategy: '選擇網域策略',
        selectAddressPortStrategy: '選擇位址連接埠策略',

        // 提示資訊
        realityProtocolNote: '注意：只有 VLESS 和 Trojan 協定支援 Reality',
        tcpFastOpenEnabled: 'TCP Fast Open: 已啟用',

        // 按鈕文字
        cancel: '取消',
        delete: '刪除',
        generate: '產生',
        yes: '是',
        no: '否',
      },

      // 出站設定表單
      outboundConfig: {
        addTitle: '新增出站設定',
        editTitle: '編輯出站設定',
        save: '儲存設定',
        importFromClipboard: '從剪貼簿匯入',
        importSuccess: '協定連結已匯入到設定表單',
        importError: '匯入失敗',
        importErrorEmpty: '剪貼簿為空',
        importErrorUnsupported: '剪貼簿內容不是支援的協定連結',
        parseError: '解析Wireguard連結失敗',

        // 基本設定
        basicSettings: '基本設定',
        tag: '標籤',
        tagPlaceholder: '請輸入出站標籤',
        protocol: '協定',
        protocolPlaceholder: '選擇協定',
        sendThrough: '發送位址',
        sendThroughPlaceholder: '本機IP位址（可選）',

        // 協定選項
        protocols: {
          freedom: 'Freedom',
          blackhole: 'Blackhole',
          dns: 'DNS',
          http: 'HTTP',
          socks: 'SOCKS',
          shadowsocks: 'Shadowsocks',
          vless: 'VLESS',
          vmess: 'VMess',
          trojan: 'Trojan',
          wireguard: 'Wireguard',
        },

        // Freedom 設定
        freedomSettings: 'Freedom 設定',
        domainStrategy: '網域策略',
        domainStrategyPlaceholder: '選擇網域策略',
        proxyProtocolVersion: '代理協定版本',
        redirectAddress: '重新導向位址',
        redirectAddressPlaceholder: '127.0.0.1:3366',

        // Fragment 設定
        fragmentSettings: 'Fragment 設定',
        fragmentEnabled: 'Fragment: 已啟用',
        fragmentDisabled: 'Fragment 已停用',
        fragmentPackets: '資料包類型',
        fragmentLength: '長度範圍',
        fragmentInterval: '間隔',

        // Noises 設定
        noisesSettings: 'Noises 設定',
        addNoise: '新增 Noise',
        noNoises: '暫無 Noise 設定，點擊上方按鈕新增 Noise',
        noiseType: '類型',
        noiseDelay: '延遲',
        noisePacket: '資料包',

        // Blackhole 設定
        blackholeSettings: 'Blackhole 設定',
        responseType: '回應類型',
        responseNone: '無回應',
        responseHttp: 'HTTP 回應',

        // DNS 設定
        dnsSettings: 'DNS 設定',
        networkType: '網路類型',
        nonIPQuery: '非IP查詢處理',
        dnsServer: 'DNS 伺服器位址',
        dnsServerPlaceholder: '*******',
        port: '連接埠',
        portPlaceholder: '53',
        drop: '丟棄',
        skip: '跳過',

        // HTTP 設定
        httpSettings: 'HTTP 設定',
        serverAddress: '伺服器位址',
        serverAddressPlaceholder: 'proxy.example.com',
        serverPort: '伺服器連接埠',
        serverPortPlaceholder: '3128',
        username: '使用者名稱',
        usernamePlaceholder: '請輸入使用者名稱',
        password: '密碼',
        passwordPlaceholder: '請輸入密碼',

        // SOCKS 設定
        socksSettings: 'SOCKS 設定',
        socksPortPlaceholder: '1080',

        // Shadowsocks 設定
        shadowsocksSettings: 'Shadowsocks 設定',
        method: '加密方式',
        methodPlaceholder: '選擇加密方式',
        ssPortPlaceholder: '8388',
        uot: 'UDP over TCP',
        uotVersion: 'UoT 版本',

        // VLESS 設定
        vlessSettings: 'VLESS 設定',
        userId: '使用者ID',
        userIdPlaceholder: '請輸入使用者ID',
        flow: '流控',
        flowPlaceholder: 'xtls-rprx-vision（可選）',
        vlessPortPlaceholder: '443',

        // VMess 設定
        vmessSettings: 'VMess 設定',
        security: '安全性',
        securityPlaceholder: '選擇安全方式',
        vmessPortPlaceholder: '443',

        // Trojan 設定
        trojanSettings: 'Trojan 設定',
        trojanPortPlaceholder: '443',

        // Wireguard 設定
        wireguardSettings: 'Wireguard 設定',
        secretKey: '私鑰',
        secretKeyPlaceholder: '請輸入私鑰',
        endpoint: '端點',
        endpointPlaceholder: 'server.example.com:51820',
        publicKey: '公鑰',
        publicKeyPlaceholder: '請輸入公鑰',
        mtu: 'MTU',
        mtuPlaceholder: '1420',
        address: '位址',
        addressPlaceholder: '********/32, fd00::2/128',
        noKernelTun: '停用核心TUN',
        reserved: '保留欄位',
        reservedPlaceholder: '0,0,0',
        workers: '工作執行緒',
        workersPlaceholder: '工作執行緒數量',
        wgDomainStrategy: '網域策略',

        // 傳輸設定
        transportSettings: '傳輸設定',
        transportType: '傳輸方式',
        transportEnabled: '傳輸方式: 已啟用',
        transportDisabled: '傳輸設定已停用',
        edit: '編輯',

        // TCP 設定
        tcpSettings: 'TCP 設定',
        headerType: '偽裝類型',
        httpVersion: 'HTTP 版本',
        httpMethod: 'HTTP 方法',
        httpPaths: 'HTTP 路徑',
        httpHeaders: 'HTTP 標頭',
        responseVersion: '回應版本',
        responseStatus: '回應狀態',
        responseReason: '回應原因',

        // XHTTP 設定
        xhttpSettings: 'XHTTP 設定',
        host: '主機',
        hostPlaceholder: 'example.com',
        path: '路徑',
        pathPlaceholder: '/',
        mode: '模式',
        headers: '標頭',
        paddingBytes: '填充位元組',
        noGRPCHeader: '停用 gRPC 標頭',
        minPostsInterval: '最小發送間隔',
        maxConcurrency: '最大並行數',
        maxConnections: '最大連線數',
        maxReuseTimes: '最大重用次數',
        maxRequestTimes: '最大請求次數',
        maxReusableSecs: '最大重用秒數',
        keepAlivePeriod: '保活週期',
        downloadAddress: '下載位址',
        downloadPort: '下載連接埠',
        downloadNetwork: '下載網路',

        // mKCP 設定
        mkcpSettings: 'mKCP 設定',
        tti: 'TTI',
        uplinkCapacity: '上行容量',
        downlinkCapacity: '下行容量',
        congestion: '壅塞控制',
        readBufferSize: '讀取緩衝區大小',
        writeBufferSize: '寫入緩衝區大小',
        domain: '網域',
        seed: '種子',

        // gRPC 設定
        grpcSettings: 'gRPC 設定',
        authority: '權威',
        serviceName: '服務名稱',
        userAgent: '使用者代理',
        multiMode: '多路模式',
        idleTimeout: '閒置逾時',
        healthCheckTimeout: '健康檢查逾時',
        permitWithoutStream: '允許無串流',
        initialWindowsSize: '初始視窗大小',

        // WebSocket 設定
        websocketSettings: 'WebSocket 設定',
        heartbeatPeriod: '心跳週期',

        // HTTP Upgrade 設定
        httpUpgradeSettings: 'HTTP Upgrade 設定',

        // 安全設定
        securitySettings: '安全設定',
        securityType: '安全類型',
        securityEnabled: '安全設定: 已啟用',
        securityDisabled: '安全設定已停用',

        // TLS 設定
        tlsSettings: 'TLS 設定',
        serverName: '伺服器名稱',
        serverNamePlaceholder: 'example.com',
        allowInsecure: '允許不安全連線',
        alpn: 'ALPN',
        fingerprint: '指紋',
        pinnedPeerCerts: '固定對等憑證',
        pinnedPeerCertsPlaceholder: '用逗號分隔的SHA256雜湊值',

        // Reality 設定
        realitySettings: 'Reality 設定',
        realityPassword: '密碼',
        shortId: '短ID',
        spiderX: 'Spider X',

        // Mux 設定
        muxSettings: 'Mux 設定',
        muxEnabled: '多路復用: 已啟用',
        muxDisabled: '多路復用已停用',
        concurrency: '並行數',
        xudpConcurrency: 'XUDP並行數',
        xudpProxyUDP443: 'XUDP代理UDP443',
        reject: '拒絕',
        allow: '允許',

        // SockOpt 設定
        sockOptSettings: 'SockOpt 設定',
        sockOptEnabled: 'SockOpt: 已啟用',
        sockOptDisabled: 'SockOpt已停用',
        mark: '標記',
        tcpMaxSeg: 'TCP最大段',
        tcpFastOpen: 'TCP快速開啟',
        tproxy: 'TProxy',
        dialerProxy: '撥號代理',
        dialerProxyPlaceholder: '請輸入代理標籤',
        tcpKeepAliveInterval: 'TCP保活間隔',
        tcpKeepAliveIdle: 'TCP保活閒置',
        tcpUserTimeout: 'TCP使用者逾時',
        tcpCongestion: 'TCP壅塞控制',
        interface: '介面',
        v6Only: '僅IPv6',
        tcpWindowClamp: 'TCP視窗限制',
        addressPortStrategy: '位址連接埠策略',

        // 安全設定新增
        realityProtocolHint: '僅 VLESS 和 Trojan 協定支援 Reality',
        realityServerNamePlaceholder: 'example.com,www.example.com',
        shortIdPlaceholder: '01234ef',

        // 其他翻譯
        serverNotFound: '未找到伺服器設定',
        updateSuccess: '出站設定已更新',
        addSuccess: '出站設定已新增',

        // 代理設定
        proxySettings: '代理設定',
        proxyTag: '代理標籤',
        proxyTagPlaceholder: '請輸入代理標籤',

        // 通用
        none: '無',
        auto: '自動',
        tcp: 'TCP',
        udp: 'UDP',
        websocket: 'WebSocket',
        http2: 'HTTP/2',
        grpc: 'gRPC',
        xhttp: 'XHTTP',
        mkcp: 'mKCP',
        httpupgrade: 'HTTP Upgrade',
        tls: 'TLS',
        reality: 'Reality',
        version0: '版本 0',
        version1: '版本 1',
        version2: '版本 2',
        off: '關閉',
        redirect: '重新導向',
        tun: 'TUN',
        original: '原始',

        // 錯誤訊息
        saveError: '儲存設定失敗',
        saveSuccess: '設定儲存成功',
        validationError: '請填寫所有必填欄位',
        tagRequired: '標籤為必填項',
        serverAddressRequired: '伺服器位址為必填項',
        serverPortRequired: '伺服器連接埠為必填項',
        userIdRequired: '使用者ID為必填項',
        passwordRequired: '密碼為必填項',
        secretKeyRequired: '私鑰為必填項',
        publicKeyRequired: '公鑰為必填項',
        endpointRequired: '端點為必填項',
      },
    },

    // 设置
    settings: {
      title: '設定',
      theme: '主題',
      language: '語言',
      proPlan: 'Pro 方案',
      subscribeToPro: '訂閱 Pro',
      messengerGroup: 'Messenger 群組',
      proActive: 'Pro 訂閱已啟用',
      proDescription: '升級到 Pro 版本以新增無限配置',
      configLimit: '配置限制',
      configLimitReached: '您已達到配置限制。升級到 Pro 版本以新增更多配置。',
      configLimitError: '已達到配置限制。升級到 Pro 版本以新增更多配置。',
      restorePurchases: '恢復購買',
      purchaseError: '購買失敗，請重試。',
      restoreSuccess: '購買恢復成功',
      restoreError: '恢復購買失敗',
      openLinkError: '開啟連結失敗',
      privacyPolicy: '隱私政策',
      clearData: '清除所有資料',
      clearDataConfirmMessage: '這將刪除所有應用程式資料，包括設定、群組、偏好與快取狀態。此操作無法復原，是否繼續？',
      clearDataSuccess: '資料已全部清除',
      clearDataError: '清除資料失敗',
      themes: {
        light: '淺色',
        dark: '深色',
        system: '跟隨系統',
      },
      languages: {
        en: 'English',
        'zh-CN': '简体中文',
        'zh-TW': '繁體中文',
        fa: 'فارسی',
      },
    },
  },
  
  fa: {
    // 通用
    common: {
      add: 'افزودن',
      edit: 'ویرایش',
      delete: 'حذف',
      save: 'ذخیره',
      saving: 'در حال ذخیره...',
      cancel: 'لغو',
      ok: 'تأیید',
      confirm: 'تأیید',
      loading: 'در حال بارگذاری...',
      connecting: 'در حال اتصال...',
      error: 'خطا',
      success: 'موفقیت',
      name: 'نام',
      url: 'آدرس',
      username: 'نام کاربری',
      password: 'رمز عبور',
      protocol: 'پروتکل',
      certificate: 'گواهی',
      group: 'گروه',
      all: 'همه',
      default: 'پیش‌فرض',
      deleteConfirmMessage: 'آیا مطمئن هستید که می‌خواهید این پیکربندی را حذف کنید؟',
      deleteError: 'حذف پیکربندی ناموفق بود',
      import: 'وارد کردن',
      importConfig: 'وارد کردن پیکربندی',
      importConfigFrom: 'وارد کردن از پیکربندی',
      selectConfigToImport: 'انتخاب پیکربندی برای وارد کردن',
      importSuccess: 'پیکربندی با موفقیت وارد شد',
      importError: 'وارد کردن پیکربندی ناموفق بود',
      unsupportedType: 'نوع پیکربندی پشتیبانی نشده',
      scanQRCode: 'اسکن کد QR',
      generateQRCode: 'تولید کردن',
      yes: 'بله',
      no: 'خیر',
      value: 'مقدار',
      unknownError: 'خطای ناشناخته',
      developing: 'در حال توسعه',
    },
    
    // 导航
    navigation: {
      home: 'خانه',
      settings: 'تنظیمات',
      addConfig: 'افزودن پیکربندی',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'مدیر UI',
      addConfiguration: 'افزودن پیکربندی',
      editGroups: 'ویرایش گروه‌ها',
      noConfigurations: 'هنوز پیکربندی وجود ندارد',
      addFirstConfig: 'اولین پیکربندی خود را اضافه کنید',
    },
    
    // 添加配置
    addConfig: {
      title: 'افزودن پیکربندی',
      selectType: 'انتخاب نوع پیکربندی',
      configName: 'نام پیکربندی',
      apiKey: 'کلید API',
      httpWarning: 'این بدان معناست که API و سایر اطلاعات حساس به صورت متن ساده ارسال می‌شوند. لطفاً اطمینان حاصل کنید که در محیط شبکه امنی هستید.',
      certTooltip: 'اگر از گواهی خودامضا استفاده می‌کنید، لطفاً پر کنید',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: 'انتخاب گروه',
      submit: 'افزودن پیکربندی',
    },

    // 配置表单
    configForm: {
      name: 'نام پیکربندی',
      namePlaceholder: 'نام پیکربندی را وارد کنید',
      protocol: 'پروتکل',
      url: 'آدرس',
      username: 'نام کاربری',
      usernamePlaceholder: 'نام کاربری را وارد کنید',
      password: 'رمز عبور',
      passwordPlaceholder: 'رمز عبور را وارد کنید',
      api: 'کلید API',
      apiPlaceholder: 'کلید API را وارد کنید',
      cert: 'گواهی',
      certTooltip: 'اگر از گواهی خودامضا استفاده می‌کنید، لطفاً پر کنید',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      publicKeyHash: 'هش کلید عمومی (SHA256)',
      publicKeyHashTooltip: 'پین کردن گواهی فقط برای تقویت بیشتر امنیت هنگام ارتباط با گواهی‌های مورد اعتماد سیستم استفاده می‌شود، گواهی‌های خودامضا همچنان در اتصال ناکام خواهند بود',
      ipAddressCertWarning: 'آدرس IP نمی‌تواند برای پین کردن گواهی SSL استفاده شود. لطفاً از نام دامنه استفاده کنید.',
      publicKeyHashPlaceholder: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456\n9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba',
      group: 'گروه',
      groupHint: 'پیکربندی به طور خودکار به گروه "همه" اضافه خواهد شد. می‌توانید به صورت اختیاری گروه‌های اضافی انتخاب کنید.',
      httpWarning: 'این بدان معناست که API و سایر اطلاعات حساس به صورت متن ساده ارسال می‌شوند. لطفاً اطمینان حاصل کنید که در محیط شبکه امنی هستید.',
      httpWarningTitle: 'هشدار امنیتی',
      connectionFailed: 'اتصال به پنل ناموفق بود. لطفاً تنظیمات خود را بررسی کنید.',
      submitError: 'ارسال پیکربندی ناموفق بود. لطفاً دوباره تلاش کنید.',
      addSUIConfig: 'افزودن پیکربندی S-UI',
      editSUIConfig: 'ویرایش پیکربندی S-UI',
      addXUIConfig: 'افزودن پیکربندی X-UI',
      editXUIConfig: 'ویرایش پیکربندی X-UI',
      add3XUIConfig: 'افزودن پیکربندی 3X-UI',
      edit3XUIConfig: 'ویرایش پیکربندی 3X-UI',
    },

    // 验证
    validation: {
      required: 'این فیلد الزامی است',
      invalidUrl: 'لطفاً یک URL معتبر وارد کنید',
    },
    
    // 分组管理
    groups: {
      title: 'مدیریت گروه‌ها',
      editGroups: 'ویرایش گروه‌ها',
      all: 'همه',
      addGroup: 'افزودن گروه',
      rename: 'تغییر نام',
      moveUp: 'انتقال به بالا',
      moveDown: 'انتقال به پایین',
      deleteGroup: 'حذف گروه',
      deleteConfirm: 'آیا مطمئن هستید که می‌خواهید این گروه را حذف کنید؟ تمام پیکربندی‌های این گروه به گروه همه منتقل خواهند شد.',
      groupName: 'نام گروه',
      enterGroupName: 'نام گروه را وارد کنید',
      renameGroup: 'تغییر نام گروه',
      newGroupName: 'نام جدید گروه',
      cannotDeleteDefault: 'نمی‌توان گروه همه را حذف کرد',
      cannotRenameDefault: 'نمی‌توان نام گروه همه را تغییر داد',
    },

    // ترجمه‌های اختصاصی 3X-UI
    threeXUI: {
      // ناوبری Drawer
      drawer: {
        overview: 'نمای کلی',
        inbounds: 'ورودی‌ها',
        routing: 'قوانین مسیریابی',
        outbounds: 'خروجی‌ها',
        backToHome: 'بازگشت به خانه',
      },

      // صفحه نمای کلی
      overview: {
        title: 'نمای کلی 3X-UI',
        loading: 'در حال بارگذاری...',
        xrayStatus: 'وضعیت Xray',
        restart: 'راه‌اندازی مجدد',
        restarting: 'در حال راه‌اندازی مجدد...',
        update: 'به‌روزرسانی',
        version: 'نسخه',
        systemResources: 'منابع سیستم',
        cpu: 'پردازنده',
        memory: 'حافظه',
        disk: 'دیسک',
        networkTraffic: 'ترافیک شبکه',
        selectXrayVersion: 'انتخاب نسخه Xray',
        install: 'نصب',
        installing: 'در حال نصب...',
        serverOffline: 'سرور آفلاین یا اتصال ناموفق',
        gettingData: 'در حال دریافت داده‌های سرور...',
        restartSuccess: 'سرویس Xray با موفقیت راه‌اندازی شد',
        restartFailed: 'راه‌اندازی مجدد سرویس Xray ناموفق بود',
        installSuccess: 'Xray {version} با موفقیت نصب شد',
        installFailed: 'نصب Xray ناموفق بود',
        confirmRestart: 'تأیید راه‌اندازی مجدد',
        confirmRestartMessage: 'آیا مطمئن هستید که می‌خواهید سرویس Xray را راه‌اندازی مجدد کنید؟ این کار تنظیمات جدید قوانین مسیریابی را اعمال خواهد کرد.',
        ipCopySuccess: 'آدرس {version} به کلیپ‌بورد کپی شد',
        ipCopyFailed: 'کپی کردن آدرس IP ناموفق بود',
      },

      // قوانین مسیریابی
      routing: {
        title: 'قوانین مسیریابی',
        add: 'افزودن',
        save: 'ذخیره',
        restart: 'راه‌اندازی مجدد',
        updateGeo: 'به‌روزرسانی Geo',
        updating: 'در حال به‌روزرسانی {percentage}%',
        noRules: 'هیچ قانون مسیریابی وجود ندارد',
        noRulesSubtext: 'روی دکمه افزودن بالا کلیک کنید تا اولین قانون مسیریابی را ایجاد کنید',
        editRule: 'ویرایش قانون',
        deleteRule: 'حذف قانون',
        confirmDelete: 'تأیید حذف',
        confirmDeleteMessage: 'آیا مطمئن هستید که می‌خواهید این قانون مسیریابی را حذف کنید؟ این عمل قابل بازگشت نیست.',
        confirmRestart: 'تأیید راه‌اندازی مجدد',
        confirmRestartMessage: 'آیا مطمئن هستید که می‌خواهید سرویس Xray را راه‌اندازی مجدد کنید؟ این کار تنظیمات جدید قوانین مسیریابی را اعمال خواهد کرد.',
        confirmUpdateGeo: 'تأیید به‌روزرسانی',
        confirmUpdateGeoMessage: 'آیا مطمئن هستید که می‌خواهید پایگاه داده geo را به‌روزرسانی کنید؟ این کار آخرین داده‌های geosite و geoip را دانلود خواهد کرد.',
        updateGeoSuccess: 'به‌روزرسانی کامل شد!',
        updateGeoFailed: 'به‌روزرسانی ناموفق بود',
        saveSuccess: 'قوانین مسیریابی ذخیره شد',
        saveFailed: 'ذخیره ناموفق بود',
        loadFailed: 'بارگذاری تنظیمات مسیریابی ناموفق بود',
        restartSuccess: 'سرویس Xray با موفقیت راه‌اندازی شد',
        restartFailed: 'راه‌اندازی مجدد سرویس Xray ناموفق بود',
        updateFailed: 'به‌روزرسانی ناموفق بود',
        unknownError: 'خطای ناشناخته',
        ruleDescription: {
          domain: 'دامنه',
          ip: 'آی‌پی',
          port: 'پورت',
          network: 'شبکه',
          protocol: 'پروتکل',
          inbound: 'ورودی',
          emptyRule: 'قانون خالی',
          rule: 'قانون {number}',
        },
      },

      // تنظیمات ورودی
      inbounds: {
        title: 'لیست ورودی‌ها',
        add: 'افزودن',
        userManagement: 'مدیریت کاربران',
        noInbounds: 'هیچ تنظیم ورودی وجود ندارد',
        noInboundsSubtext: 'روی دکمه بالا کلیک کنید تا اولین تنظیم ورودی را اضافه کنید',
        editConfig: 'ویرایش تنظیمات',
        exportLinks: 'صادرات لینک‌ها',
        resetTraffic: 'بازنشانی ترافیک',
        exportJson: 'صادرات JSON',
        enable: 'فعال‌سازی',
        disable: 'غیرفعال‌سازی',
        deleteConfig: 'حذف تنظیمات',
        confirmDelete: 'تأیید حذف',
        confirmDeleteMessage: 'آیا مطمئن هستید که می‌خواهید این تنظیم ورودی را حذف کنید؟ این عمل قابل بازگشت نیست.',
        confirmResetTraffic: 'تأیید بازنشانی',
        confirmResetTrafficMessage: 'آیا مطمئن هستید که می‌خواهید آمار ترافیک این تنظیم ورودی را بازنشانی کنید؟',
        configNotFound: 'تنظیمات یافت نشد',
        loadFailed: 'بارگذاری لیست ورودی‌ها ناموفق بود',
        enableSuccess: 'تنظیمات فعال شد',
        disableSuccess: 'تنظیمات غیرفعال شد',
        operationFailed: 'عملیات ناموفق بود',
        resetSuccess: 'ترافیک بازنشانی شد',
        resetFailed: 'بازنشانی ناموفق بود',
        exportSuccess: 'JSON تنظیمات در کلیپ‌بورد کپی شد',
        exportFailed: 'صادرات ناموفق بود',
        deleteSuccess: 'تنظیمات حذف شد',
        deleteFailed: 'حذف ناموفق بود',
        userManagementComingSoon: 'قابلیت مدیریت کاربران به زودی ارائه خواهد شد',
        status: {
          valid: 'معتبر',
          invalid: 'نامعتبر',
        },
      },

      // تنظیمات قوانین مسیریابی
      ruleConfig: {
        title: 'افزودن قانون مسیریابی',
        editTitle: 'ویرایش قانون مسیریابی',
        domainMatcher: 'تطبیق‌دهنده دامنه',
        domain: 'دامنه',
        domainPlaceholder: 'دامنه‌ها را وارد کنید، با کاما جدا شوند',
        ip: 'آدرس IP',
        ipPlaceholder: 'آدرس‌های IP یا CIDR را وارد کنید، با کاما جدا شوند',
        port: 'پورت',
        portPlaceholder: 'مثال: 53,443,1000-2000',
        sourcePort: 'پورت مبدأ',
        sourcePortPlaceholder: 'مثال: 53,443,1000-2000',
        network: 'نوع شبکه',
        source: 'آدرس مبدأ',
        sourcePlaceholder: 'آدرس‌های IP مبدأ را وارد کنید، با کاما جدا شوند',
        user: 'کاربر',
        userPlaceholder: 'ایمیل‌های کاربران را وارد کنید، با کاما جدا شوند',
        inboundTag: 'برچسب ورودی',
        protocol: 'پروتکل',
        httpAttrs: 'ویژگی‌های HTTP',
        attrKeyPlaceholder: ':method',
        attrValuePlaceholder: 'GET',
        outboundTag: 'برچسب خروجی',
        balancerTag: 'برچسب متعادل‌کننده',
        balancerTagPlaceholder: 'برچسب متعادل‌کننده را وارد کنید',
        saveRule: 'ذخیره قانون',
        configNotFound: 'اطلاعات تنظیمات یافت نشد',
        saveSuccess: 'قانون مسیریابی اضافه شد',
        updateSuccess: 'قانون مسیریابی به‌روزرسانی شد',
        saveFailed: 'ذخیره ناموفق بود',
      },

      // صادرات لینک‌ها
      exportLinks: {
        title: 'صادرات لینک‌ها',
        noLinks: 'هیچ لینک کاربری در دسترس نیست',
        close: 'بستن',
        copySuccess: 'لینک {label} در کلیپ‌بورد کپی شد',
        copyFailed: 'کپی ناموفق بود',
        generateFailed: 'تولید لینک‌ها ناموفق بود',
        user: 'کاربر {number}',
      },

      // تنظیمات خروجی
      outbounds: {
        title: 'لیست خروجی‌ها',
        add: 'افزودن',
        restart: 'راه‌اندازی مجدد',
        restarting: 'در حال راه‌اندازی مجدد...',
        noOutbounds: 'هیچ تنظیم خروجی وجود ندارد',
        noOutboundsSubtext: 'روی دکمه افزودن بالا کلیک کنید تا تنظیم خروجی جدید ایجاد کنید',
        editConfig: 'ویرایش تنظیمات',
        deleteConfig: 'حذف تنظیمات',
        confirmDelete: 'تأیید حذف',
        confirmDeleteMessage: 'آیا مطمئن هستید که می‌خواهید این تنظیم خروجی را حذف کنید؟ این عمل قابل بازگشت نیست.',
        confirmRestart: 'تأیید راه‌اندازی مجدد',
        confirmRestartMessage: 'آیا مطمئن هستید که می‌خواهید سرویس Xray را راه‌اندازی مجدد کنید؟ این کار تنظیمات جدید خروجی را اعمال خواهد کرد.',
        loadFailed: 'بارگذاری لیست خروجی‌ها ناموفق بود',
        deleteSuccess: 'تنظیمات خروجی حذف شد',
        deleteFailed: 'حذف ناموفق بود',
        restartSuccess: 'سرویس Xray با موفقیت راه‌اندازی شد',
        restartFailed: 'راه‌اندازی مجدد سرویس Xray ناموفق بود',
        outboundName: 'خروجی-{number}',

        // تنظیمات WARP
        warp: {
          title: 'تنظیمات WARP',
          addWarp: 'افزودن WARP',
          addWarpMessage: 'هنوز تنظیمات WARP دریافت نشده، آیا الان اضافه کنید؟',
          addOutbound: 'افزودن خروجی',
          delete: 'حذف',
          confirmDeleteWarp: 'تأیید حذف',
          confirmDeleteWarpMessage: 'آیا مطمئن هستید که می‌خواهید تنظیمات WARP را حذف کنید؟ این عمل قابل بازگشت نیست.',
          deleteWarpSuccess: 'تنظیمات WARP حذف شد',
          deleteWarpFailed: 'حذف تنظیمات WARP ناموفق بود',
          getWarpFailed: 'دریافت تنظیمات WARP ناموفق بود',
          warpExists: 'تنظیم خروجی WARP از قبل وجود دارد',
          name: 'نام:',
          type: 'نوع:',
          accountType: 'نوع حساب:',
          ipv4Address: 'آدرس IPv4:',
          ipv6Address: 'آدرس IPv6:',
          endpoint: 'نقطه پایانی:',
          expiryTime: 'زمان انقضا:',
          unknown: 'ناشناخته',
        },
      },
    },

    // 设置
    settings: {
      title: 'تنظیمات',
      theme: 'تم',
      language: 'زبان',
      proPlan: 'طرح Pro',
      subscribeToPro: 'اشتراک Pro',
      messengerGroup: 'گروه Messenger',
      proActive: 'اشتراک Pro فعال است',
      proDescription: 'برای افزودن پیکربندی‌های نامحدود به Pro ارتقا دهید',
      configLimit: 'محدودیت پیکربندی',
      configLimitReached: 'شما به محدودیت پیکربندی رسیده‌اید. برای افزودن بیشتر به Pro ارتقا دهید.',
      configLimitError: 'محدودیت پیکربندی رسیده است. برای افزودن پیکربندی‌های بیشتر به Pro ارتقا دهید.',
      restorePurchases: 'بازیابی خریدها',
      purchaseError: 'خرید ناموفق بود. لطفاً دوباره تلاش کنید.',
      restoreSuccess: 'خریدها با موفقیت بازیابی شد',
      restoreError: 'بازیابی خریدها ناموفق بود',
      openLinkError: 'باز کردن لینک ناموفق بود',
      privacyPolicy: 'سیاست حفظ حریم خصوصی',
      clearData: 'حذف تمام داده‌ها',
      clearDataConfirmMessage: 'این کار تمام داده‌های برنامه، از جمله تنظیمات، گروه‌ها و وضعیت‌های کش‌شده را حذف می‌کند. این عمل قابل بازگشت نیست. ادامه می‌دهید؟',
      clearDataSuccess: 'تمام داده‌ها با موفقیت حذف شدند',
      clearDataError: 'حذف داده‌ها ناموفق بود',
      themes: {
        light: 'روشن',
        dark: 'تیره',
        system: 'پیروی از سیستم',
      },
      languages: {
        en: 'English',
        'zh-CN': '简体中文',
        'zh-TW': '繁體中文',
        fa: 'فارسی',
      },
    },
  },
};

// 获取当前语言
export const getCurrentLanguage = (): Language => {
  const locale = Localization.getLocales()[0];
  const languageTag = locale.languageTag;
  
  if (languageTag.startsWith('zh')) {
    return locale.regionCode === 'TW' || locale.regionCode === 'HK' ? 'zh-TW' : 'zh-CN';
  }
  
  if (languageTag.startsWith('fa')) {
    return 'fa';
  }
  
  return 'en';
};

// 翻译函数
export const t = (key: string, language: Language = 'en'): string => {
  const keys = key.split('.');
  let value: any = translations[language];
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // 如果找不到翻译，回退到英文
      value = translations.en;
      for (const fallbackKey of keys) {
        if (value && typeof value === 'object' && fallbackKey in value) {
          value = value[fallbackKey];
        } else {
          return key; // 如果英文也没有，返回原始 key
        }
      }
      break;
    }
  }
  
  return typeof value === 'string' ? value : key;
};
